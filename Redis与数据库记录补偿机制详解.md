# Redis与数据库记录补偿机制详解

## 目录
- [1. 概述](#1-概述)
- [2. 补偿机制架构](#2-补偿机制架构)
- [3. 核心组件](#3-核心组件)
- [4. 补偿流程详解](#4-补偿流程详解)
- [5. 数据对比逻辑](#5-数据对比逻辑)
- [6. 补偿执行机制](#6-补偿执行机制)
- [7. 定时任务调度](#7-定时任务调度)
- [8. 最佳实践](#8-最佳实践)

## 1. 概述

在大麦网高并发购票系统中，为了保证Redis缓存与数据库之间的数据一致性，实现了一套完整的补偿机制。该机制通过对比Redis中的操作记录与数据库中的实际数据，自动发现并修复数据不一致的问题。

### 1.1 补偿机制的作用

- **数据一致性保障**：确保Redis缓存与数据库数据的最终一致性
- **异常恢复**：自动处理因网络异常、服务重启等导致的数据不一致
- **业务连续性**：在不影响正常业务的情况下进行数据修复
- **监控告警**：及时发现和处理数据异常情况

### 1.2 适用场景

- 订单创建过程中的数据同步异常
- 缓存更新失败导致的数据不一致
- 分布式事务执行过程中的部分失败
- 系统重启后的数据状态恢复

## 2. 补偿机制架构

### 2.1 整体架构图

```mermaid
graph TB
    A[定时任务调度] --> B[获取待处理任务]
    B --> C[执行对账任务]
    C --> D[Redis记录查询]
    C --> E[数据库记录查询]
    D --> F[数据对比分析]
    E --> F
    F --> G{是否存在差异}
    G -->|是| H[生成补偿数据]
    G -->|否| I[标记任务完成]
    H --> J[执行补偿操作]
    J --> K[更新Redis缓存]
    J --> L[更新数据库状态]
    K --> M[补偿完成]
    L --> M
    I --> M
```

### 2.2 数据流向

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   业务操作       │───▶│   Redis记录      │───▶│   补偿任务       │
│ (订单创建等)     │    │ (操作流水)       │    │ (ProgramRecordTask) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据库记录     │    │   对比分析       │    │   补偿执行       │
│ (订单数据)       │    │ (差异检测)       │    │ (数据修复)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 3. 核心组件

### 3.1 ProgramRecordTask - 补偿任务实体

```java
@Data
@TableName("d_program_record_task")
public class ProgramRecordTask extends BaseTableData implements Serializable {
    
    /**
     * 主键id
     */
    private Long id;
    
    /**
     * 节目表id
     */
    private Long programId;

    /**
     * 处理状态 1:未处理 2:已处理
     */
    private Integer handleStatus;
}
```

### 3.2 OrderTaskService - 核心补偿服务

负责执行具体的数据对比和补偿逻辑：

```java
@Service
public class OrderTaskService {
    
    /**
     * 执行对账任务
     * @param programId 节目ID
     * @return 补偿任务数据
     */
    public ReconciliationTaskData reconciliationTask(Long programId) {
        // 1. 查询Redis中的节目记录
        Map<String, String> programRecordMap = redisCache.getAllMapForHash(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId), String.class);
        
        // 2. 对比Redis和数据库记录
        ExaminationSimpleResult examinationSimpleResult = reconciliationQuerySimple(programId, programRecordMap);
        
        // 3. 生成补偿数据
        Map<String, ProgramRecord> addRedisRecordData = compensateRedisRecord(programId, needToRedisRecordMap, programRecordMap);
        
        return reconciliationTaskData;
    }
}
```

### 3.3 ProgramRecordHandler - 记录处理器

负责向Redis添加补偿记录和更新数据库状态：

```java
@Component
public class ProgramRecordHandler {
    
    /**
     * 向Redis中添加补偿的记录，从未完成记录中转移到完整的记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(int retryCount, Long programId, 
                   Map<String, ProgramRecord> completeRedisCordMap,
                   Map<String, String> totalProgramRecordMap) {
        
        // 1. 更新数据库中订单相关表的对账状态
        for (String key : keyList) {
            String[] split = SplitUtil.toSplit(key);
            Long identifierId = Long.valueOf(split[0]);
            Long userId = Long.valueOf(split[1]);
            updateDbOrderTicketUserRecordStatus(programId, identifierId, userId, 
                ReconciliationStatus.RECONCILIATION_SUCCESS);
        }
        
        // 2. 删除旧的Redis记录
        redisCache.delForHash(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId),
            totalProgramRecordMap.keySet());
        
        // 3. 添加完整的记录到Redis
        redisCache.putHash(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD_FINISH, programId), 
            completeRedisCordMap);
    }
}
```

### 3.4 ReconciliationTask - 定时任务调度器

```java
@Component
public class ReconciliationTask {
    
    // 每分钟执行一次对账任务（当前被注释，可根据需要开启）
    //@Scheduled(cron = "0 0/1 * * * ? ")
    public void reconciliationTask() {
        BusinessThreadPool.execute(() -> {
            // 1. 查询3分钟前创建的未处理任务
            ProgramRecordTaskListDto dto = new ProgramRecordTaskListDto();
            dto.setHandleStatus(HandleStatus.NO_HANDLE.getCode());
            dto.setCreateTime(DateUtils.addMinute(DateUtils.now(), -3));
            
            // 2. 获取待处理的节目ID列表
            List<ProgramRecordTaskVo> taskList = programClient.select(dto).getData();
            
            // 3. 执行对账任务
            for (Long programId : programIdSet) {
                orderTaskService.reconciliationTask(programId);
            }
            
            // 4. 更新任务状态为已处理
            programClient.update(updateDto);
        });
    }
}
```

## 4. 补偿流程详解

### 4.1 补偿任务创建

在订单创建过程中，系统会自动创建补偿任务：

```java
// 在ProgramOrderService.doCreateV2方法中
public String doCreateV2(ProgramOrderCreateDto programOrderCreateDto,
                        CreateOrderTemporaryData createOrderTemporaryData,
                        Integer orderVersion) {
    
    // 构建订单创建参数
    OrderCreateDto orderCreateDto = buildCreateOrderParamV2(/*...*/);
    
    // 插入节目记录任务（异步执行）
    BusinessThreadPool.execute(() -> createProgramRecordTask(orderCreateMq.getProgramId()));
    
    // 异步创建订单
    String orderNumber = createOrderByMq(orderCreateMq, createOrderTemporaryData.getPurchaseSeatList());
    
    return orderNumber;
}

public void createProgramRecordTask(Long programId) {
    ProgramRecordTask programRecordTask = new ProgramRecordTask();
    programRecordTask.setId(uidGenerator.getUid());
    programRecordTask.setProgramId(programId);
    programRecordTask.setCreateTime(DateUtils.now());
    programRecordTask.setEditTime(DateUtils.now());
    programRecordTaskMapper.insert(programRecordTask);
}
```

### 4.2 数据对比流程

#### 4.2.1 Redis记录查询

```java
// 查询Redis中的节目操作记录
Map<String, String> programRecordMap = redisCache.getAllMapForHash(
    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId), String.class);
```

#### 4.2.2 数据库记录查询

```java
// 查询数据库中的订单记录
List<OrderTicketUserRecord> dbOrderTicketUserRecordList = 
    orderTicketUserRecordMapper.selectList(
        Wrappers.lambdaQuery(OrderTicketUserRecord.class)
            .eq(OrderTicketUserRecord::getProgramId, programId)
            .eq(OrderTicketUserRecord::getIdentifierId, identifierId)
            .eq(OrderTicketUserRecord::getUserId, userId)
            .eq(OrderTicketUserRecord::getRecordType, dbRecordType)
    );
```

#### 4.2.3 记录对比分析

```java
public ExaminationRecordTypeResult reconciliationQueryRecordType(Long programId, 
                                                               Long identifierId, 
                                                               Long userId, 
                                                               String dbRecordType, 
                                                               ProgramRecord programRecord) {
    
    // 查询数据库中的购票人订单记录
    List<OrderTicketUserRecord> dbOrderTicketUserRecordList = /*...*/;
    
    // 情况1：数据库没有，Redis有记录
    if (CollectionUtil.isEmpty(dbOrderTicketUserRecordList) && Objects.nonNull(programRecord)) {
        Map<Long, SeatRecord> redisSeatRecordMap = getRedisSeatRecordMap(programRecord);
        ExaminationSeatResult examinationResult = executeExaminationSeat(redisSeatRecordMap, null);
        return new ExaminationRecordTypeResult(RecordType.getCodeByValue(dbRecordType), dbRecordType, examinationResult);
    }
    
    // 情况2：数据库有，Redis没有记录
    if (CollectionUtil.isNotEmpty(dbOrderTicketUserRecordList) && Objects.isNull(programRecord)) {
        Map<Long, OrderTicketUserRecord> dbOrderTicketUserRecordMap = /*...*/;
        ExaminationSeatResult examinationResult = executeExaminationSeat(null, dbOrderTicketUserRecordMap);
        return new ExaminationRecordTypeResult(RecordType.getCodeByValue(dbRecordType), dbRecordType, examinationResult);
    }
    
    // 情况3：数据库和Redis都有记录
    Map<Long, SeatRecord> redisSeatRecordMap = getRedisSeatRecordMap(programRecord);
    Map<Long, OrderTicketUserRecord> dbOrderTicketUserRecordMap = /*...*/;
    ExaminationSeatResult examinationResult = executeExaminationSeat(redisSeatRecordMap, dbOrderTicketUserRecordMap);
    return new ExaminationRecordTypeResult(RecordType.getCodeByValue(dbRecordType), dbRecordType, examinationResult);
}
```

### 4.3 座位记录对比

```java
public ExaminationSeatResult executeExaminationSeat(Map<Long, SeatRecord> redisSeatRecordMap, 
                                                   Map<Long, OrderTicketUserRecord> dbOrderTicketUserRecordMap) {
    
    int redisStandardStatisticCount = 0;
    List<SeatRecord> needToDbSeatRecordList = new ArrayList<>();
    int dbStandardStatisticCount = 0;
    List<OrderTicketUserRecord> needToRedisSeatRecordList = new ArrayList<>();
    
    // 以Redis为准进行对比
    if (Objects.nonNull(redisSeatRecordMap)) {
        redisStandardStatisticCount = redisSeatRecordMap.size();
        for (Map.Entry<Long, SeatRecord> entry : redisSeatRecordMap.entrySet()) {
            Long seatId = entry.getKey();
            if (Objects.isNull(dbOrderTicketUserRecordMap) || 
                !dbOrderTicketUserRecordMap.containsKey(seatId)) {
                needToDbSeatRecordList.add(entry.getValue());
            }
        }
    }
    
    // 以数据库为准进行对比
    if (Objects.nonNull(dbOrderTicketUserRecordMap)) {
        dbStandardStatisticCount = dbOrderTicketUserRecordMap.size();
        for (Map.Entry<Long, OrderTicketUserRecord> entry : dbOrderTicketUserRecordMap.entrySet()) {
            Long seatId = entry.getKey();
            if (Objects.isNull(redisSeatRecordMap) || 
                !redisSeatRecordMap.containsKey(seatId)) {
                needToRedisSeatRecordList.add(entry.getValue());
            }
        }
    }
    
    return new ExaminationSeatResult(redisStandardStatisticCount, needToDbSeatRecordList,
                                   dbStandardStatisticCount, needToRedisSeatRecordList);
}
```

## 5. 数据对比逻辑

### 5.1 对比维度

补偿机制从以下几个维度进行数据对比：

1. **记录存在性对比**：检查Redis和数据库中记录的存在性
2. **座位数据对比**：对比具体的座位信息
3. **数量统计对比**：对比记录数量是否一致
4. **状态一致性对比**：检查订单状态是否同步

### 5.2 对比结果分类

```java
public class ExaminationSeatResult {
    private int redisStandardStatisticCount;        // Redis标准统计数量
    private List<SeatRecord> needToDbSeatRecordList; // 需要补充到数据库的座位记录
    private int dbStandardStatisticCount;           // 数据库标准统计数量
    private List<OrderTicketUserRecord> needToRedisSeatRecordList; // 需要补充到Redis的座位记录
}
```

### 5.3 差异类型处理

- **Redis有，数据库无**：需要向数据库补充记录
- **数据库有，Redis无**：需要向Redis补充记录
- **两者都有但不一致**：需要进行数据校正
- **两者都无**：正常情况，无需处理

## 6. 补偿执行机制

### 6.1 逆向还原算法

补偿机制的核心是逆向还原算法，通过分析操作记录来重建正确的数据状态：

```java
/**
 * 逆向还原单笔订单的所有 ProgramRecord
 * @param programRecords 按时间正序排列的记录列表，顺序为reduce、changeStatus、increase
 * @param ticketCategoryRemainNumberMap 票档剩余数量映射，会被本方法原地回退
 */
public void restoreSingleOrder(List<ProgramRecord> programRecords,
                              Map<Long, Long> ticketCategoryRemainNumberMap) {

    // 1. 将"从最早到最新"的列表倒过来，increase、changeStatus、reduce 最新的记录先还原
    Collections.reverse(programRecords);

    // 2. 逐条记录做逆向"撤销"
    for (ProgramRecord programRecord : programRecords) {
        String recordType = programRecord.getRecordType();

        // 2.1 统计本条记录中，每个票档的变化数量
        Map<Long, Long> changeAmtMap = programRecord.getTicketCategoryRecordList().stream()
            .collect(Collectors.groupingBy(TicketCategoryRecord::getTicketCategoryId,
                    Collectors.summingLong(tcr -> tcr.getSeatRecordList().size())));

        // 2.2 对本条记录每个票档做逆向计算
        for (Map.Entry<Long, Long> entry : changeAmtMap.entrySet()) {
            Long categoryId = entry.getKey();
            long changeAmt = entry.getValue();
            long current = ticketCategoryRemainNumberMap.getOrDefault(categoryId, 0L);

            long beforeAmount;
            long afterAmount;

            if (Objects.equals(recordType, RecordType.REDUCE.getValue())) {
                // 原操作扣减 -> 逆向要加回
                afterAmount = current;
                beforeAmount = current + changeAmt;
                ticketCategoryRemainNumberMap.put(categoryId, beforeAmount);

            } else if (Objects.equals(recordType, RecordType.INCREASE.getValue())) {
                // 原操作恢复 -> 逆向要再扣减
                afterAmount = current;
                beforeAmount = current - changeAmt;
                ticketCategoryRemainNumberMap.put(categoryId, beforeAmount);

            } else {
                // 状态变更，不改票数
                beforeAmount = current;
                afterAmount = current;
            }

            // 2.3 回填所有对应 TicketCategoryRecord 的字段
            for (TicketCategoryRecord tcr : programRecord.getTicketCategoryRecordList()) {
                if (tcr.getTicketCategoryId().equals(categoryId)) {
                    tcr.setBeforeAmount(beforeAmount);
                    tcr.setAfterAmount(afterAmount);
                    tcr.setChangeAmount(Objects.equals(recordType, RecordType.CHANGE_STATUS.getValue()) ? 0L : changeAmt);
                }
            }
        }
    }

    // 3. 还原完毕后，如果后续需要再按正序使用 programRecords，可再反转回来
    Collections.reverse(programRecords);
}
```

### 6.2 补偿数据生成

```java
public Map<String, ProgramRecord> compensateRedisRecord(Long programId,
                                                       Map<String, List<ProgramRecord>> needToRedisRecordMap,
                                                       Map<String, String> programRecordMap) {

    // 1. 获取需要补充的票档ID集合
    Set<Long> ticketCategoryIdSet = getTicketCategoryIdSet(needToRedisRecordMap);

    // 2. 查询节目票档信息
    TicketCategoryListDto ticketCategoryListDto = new TicketCategoryListDto();
    ticketCategoryListDto.setProgramId(programId);
    ticketCategoryListDto.setTicketCategoryIdList(ticketCategoryIdSet);
    ApiResponse<List<TicketCategoryDetailVo>> programApiResponse = programClient.selectList(ticketCategoryListDto);

    // 3. 构建票档剩余数量映射
    Map<Long, Long> ticketCategoryRemainNumberMap = programApiResponse.getData().stream()
        .collect(Collectors.toMap(TicketCategoryDetailVo::getId, TicketCategoryDetailVo::getRemainNumber));

    // 4. 逆向还原每个订单的记录
    for (Map.Entry<String, List<ProgramRecord>> programRecordEntry : needToRedisRecordMap.entrySet()) {
        List<ProgramRecord> programRecordList = programRecordEntry.getValue();
        restoreSingleOrder(programRecordList, ticketCategoryRemainNumberMap);
    }

    // 5. 添加记录到Redis
    return addRedisRecord(programId, needToRedisRecordMap, programRecordMap);
}
```

### 6.3 Redis记录添加

```java
public Map<String, ProgramRecord> addRedisRecord(Long programId,
                                                Map<String, List<ProgramRecord>> needToRedisRecordMap,
                                                Map<String, String> programRecordMap) {

    Set<Long> ticketCategoryIdSet = getTicketCategoryIdSet(needToRedisRecordMap);

    // 1. 构建要向Redis添加记录的结构
    Map<String, ProgramRecord> completeRedisCordMap = new HashMap<>(64);
    for (Map.Entry<String, List<ProgramRecord>> redisRecordEntry : needToRedisRecordMap.entrySet()) {
        String key = redisRecordEntry.getKey();
        List<ProgramRecord> programRecordList = redisRecordEntry.getValue();
        for (ProgramRecord programRecord : programRecordList) {
            completeRedisCordMap.put(programRecord.getRecordType() + GLIDE_LINE + key, programRecord);
        }
    }

    // 2. 清理相关的Redis缓存数据
    for (Long ticketCategoryId : ticketCategoryIdSet) {
        // 删除Redis中的座位数据
        seatHandler.delRedisSeatData(programId, ticketCategoryId);
        // 删除Redis中的余票数量
        ticketRemainNumberHandler.delRedisSeatData(programId, ticketCategoryId);
    }

    // 3. 向Redis中添加补偿记录
    programRecordHandler.add(0, programId, completeRedisCordMap, programRecordMap);

    return completeRedisCordMap;
}
```

### 6.4 数据库状态更新

```java
@Transactional(rollbackFor = Exception.class)
public int updateDbOrderTicketUserRecordStatus(Long programId, Long identifierId, Long userId,
                                              ReconciliationStatus reconciliationStatus) {

    // 1. 查询需要更新的订单
    List<Order> orderList = orderMapper.selectList(
        Wrappers.lambdaQuery(Order.class)
            .eq(Order::getProgramId, programId)
            .eq(Order::getIdentifierId, identifierId)
            .eq(Order::getUserId, userId)
            .eq(Order::getReconciliationStatus, ReconciliationStatus.RECONCILIATION_NO.getCode())
    );

    if (CollectionUtil.isEmpty(orderList)) {
        return 0;
    }

    Long orderNumber = orderList.get(0).getOrderNumber();

    // 2. 更新订单的对账状态
    Order updateOrder = new Order();
    updateOrder.setReconciliationStatus(reconciliationStatus.getCode());
    orderMapper.update(updateOrder, Wrappers.lambdaUpdate(Order.class)
        .eq(Order::getProgramId, programId)
        .eq(Order::getIdentifierId, identifierId)
        .eq(Order::getUserId, userId)
        .eq(Order::getReconciliationStatus, ReconciliationStatus.RECONCILIATION_NO.getCode()));

    // 3. 更新购票人订单的对账状态
    OrderTicketUser updateOrderTicketUser = new OrderTicketUser();
    updateOrderTicketUser.setReconciliationStatus(reconciliationStatus.getCode());
    orderTicketUserMapper.update(updateOrderTicketUser, Wrappers.lambdaUpdate(OrderTicketUser.class)
        .eq(OrderTicketUser::getOrderNumber, orderNumber)
        .eq(OrderTicketUser::getReconciliationStatus, ReconciliationStatus.RECONCILIATION_NO.getCode()));

    // 4. 更新订单节目的处理状态
    OrderProgram updateOrderProgram = new OrderProgram();
    updateOrderProgram.setHandleStatus(HandleStatus.YES_HANDLE.getCode());
    orderProgramMapper.update(updateOrderProgram, Wrappers.lambdaUpdate(OrderProgram.class)
        .eq(OrderProgram::getOrderNumber, orderNumber)
        .eq(OrderProgram::getHandleStatus, HandleStatus.NO_HANDLE.getCode())
        .eq(OrderProgram::getProgramId, programId));

    // 5. 更新购票人订单记录的对账状态
    OrderTicketUserRecord updateOrderTicketUserRecord = new OrderTicketUserRecord();
    updateOrderTicketUserRecord.setReconciliationStatus(reconciliationStatus.getCode());
    return orderTicketUserRecordMapper.update(updateOrderTicketUserRecord,
        Wrappers.lambdaUpdate(OrderTicketUserRecord.class)
            .eq(OrderTicketUserRecord::getOrderNumber, orderNumber)
            .eq(OrderTicketUserRecord::getReconciliationStatus, ReconciliationStatus.RECONCILIATION_NO.getCode()));
}
```

## 7. 定时任务调度

### 7.1 任务调度策略

```java
@Component
public class ReconciliationTask {

    // 定时任务配置（当前被注释，可根据需要开启）
    //@Scheduled(cron = "0 0/1 * * * ? ") // 每分钟执行一次
    public void reconciliationTask() {
        BusinessThreadPool.execute(() -> {
            try {
                log.info("对账任务执行");

                // 1. 查询3分钟前创建的未处理任务
                ProgramRecordTaskListDto programRecordTaskListDto = new ProgramRecordTaskListDto();
                programRecordTaskListDto.setHandleStatus(HandleStatus.NO_HANDLE.getCode());
                programRecordTaskListDto.setCreateTime(DateUtils.addMinute(DateUtils.now(), -3));

                // 2. 获取任务列表
                ApiResponse<List<ProgramRecordTaskVo>> listApiResponse = programClient.select(programRecordTaskListDto);
                if (!Objects.equals(listApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                    log.error("获取节目对账记录任务集合失败 dto : {} message: {}",
                        JSON.toJSONString(programRecordTaskListDto), listApiResponse.getMessage());
                    return;
                }

                List<ProgramRecordTaskVo> programRecordTaskVoList = listApiResponse.getData();
                if (CollectionUtil.isEmpty(programRecordTaskVoList)) {
                    log.warn("获取节目对账记录任务集合为空 dto : {}",
                        JSON.toJSONString(programRecordTaskListDto));
                    return;
                }

                // 3. 提取节目ID和创建时间
                Set<Long> programIdSet = new HashSet<>();
                Set<Date> createTimeSet = new HashSet<>();
                for (ProgramRecordTaskVo programRecordTaskVo : programRecordTaskVoList) {
                    programIdSet.add(programRecordTaskVo.getProgramId());
                    createTimeSet.add(programRecordTaskVo.getCreateTime());
                }

                // 4. 执行对账任务
                for (Long programId : programIdSet) {
                    orderTaskService.reconciliationTask(programId);
                }

                // 5. 更新任务状态为已处理
                ProgramRecordTaskUpdateDto programRecordTaskUpdateDto = new ProgramRecordTaskUpdateDto();
                programRecordTaskUpdateDto.setBeforeHandleStatus(HandleStatus.NO_HANDLE.getCode());
                programRecordTaskUpdateDto.setAfterHandleStatus(HandleStatus.YES_HANDLE.getCode());
                programRecordTaskUpdateDto.setCreateTimeSet(createTimeSet);

                ApiResponse<Integer> updateApiResponse = programClient.update(programRecordTaskUpdateDto);
                if (!Objects.equals(updateApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                    log.error("更新节目对账记录任务失败 dto : {} message: {}",
                        JSON.toJSONString(programRecordTaskUpdateDto), updateApiResponse.getMessage());
                }

            } catch (Exception e) {
                log.error("reconciliation task error", e);
            }
        });
    }
}
```

### 7.2 任务执行时机

- **延迟执行**：任务创建3分钟后才执行，确保相关操作已完成
- **定时触发**：通过定时任务定期检查和处理
- **异步处理**：使用线程池异步执行，不阻塞主流程
- **批量处理**：一次性处理多个节目的对账任务

### 7.3 任务状态管理

```java
public enum HandleStatus {
    NO_HANDLE(1, "未处理"),
    YES_HANDLE(2, "已处理");

    private final Integer code;
    private final String value;
}

public enum ReconciliationStatus {
    RECONCILIATION_NO(1, "未对账"),
    RECONCILIATION_SUCCESS(2, "对账成功");

    private final Integer code;
    private final String value;
}
```

## 8. 最佳实践

### 8.1 监控和告警

```java
// 监控补偿任务执行情况
@Component
public class ReconciliationMonitor {

    public void monitorReconciliationTask() {
        // 1. 监控待处理任务数量
        long pendingTaskCount = getPendingTaskCount();
        if (pendingTaskCount > THRESHOLD) {
            sendAlert("补偿任务积压告警", "待处理任务数量: " + pendingTaskCount);
        }

        // 2. 监控补偿成功率
        double successRate = getReconciliationSuccessRate();
        if (successRate < MIN_SUCCESS_RATE) {
            sendAlert("补偿成功率告警", "成功率: " + successRate);
        }

        // 3. 监控数据不一致情况
        List<DataInconsistency> inconsistencies = detectDataInconsistencies();
        if (!inconsistencies.isEmpty()) {
            sendAlert("数据不一致告警", "发现 " + inconsistencies.size() + " 处数据不一致");
        }
    }
}
```

### 8.2 性能优化

1. **批量处理**：
```java
// 批量更新数据库状态，减少数据库交互次数
public void batchUpdateReconciliationStatus(List<ReconciliationTask> tasks) {
    // 按节目ID分组批量处理
    Map<Long, List<ReconciliationTask>> groupedTasks = tasks.stream()
        .collect(Collectors.groupingBy(ReconciliationTask::getProgramId));

    for (Map.Entry<Long, List<ReconciliationTask>> entry : groupedTasks.entrySet()) {
        batchUpdateByProgramId(entry.getKey(), entry.getValue());
    }
}
```

2. **缓存优化**：
```java
// 缓存票档信息，减少重复查询
@Cacheable(value = "ticketCategory", key = "#programId + '_' + #ticketCategoryId")
public TicketCategoryDetailVo getTicketCategoryDetail(Long programId, Long ticketCategoryId) {
    return ticketCategoryService.getDetail(programId, ticketCategoryId);
}
```

3. **异步处理**：
```java
// 使用异步处理提高响应速度
@Async("reconciliationExecutor")
public CompletableFuture<ReconciliationResult> asyncReconciliation(Long programId) {
    ReconciliationResult result = doReconciliation(programId);
    return CompletableFuture.completedFuture(result);
}
```

### 8.3 错误处理和重试

```java
@Component
public class ReconciliationRetryHandler {

    private static final int MAX_RETRY_COUNT = 5;
    private static final long RETRY_DELAY_MS = 1000;

    public void executeWithRetry(Runnable task, int retryCount) {
        try {
            task.run();
        } catch (Exception e) {
            if (retryCount < MAX_RETRY_COUNT) {
                log.warn("补偿任务执行失败，进行重试. retryCount: {}", retryCount, e);
                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ex);
                }
                executeWithRetry(task, retryCount + 1);
            } else {
                log.error("补偿任务执行失败，超过最大重试次数: {}", MAX_RETRY_COUNT, e);
                throw new RuntimeException("补偿任务执行失败", e);
            }
        }
    }
}
```

### 8.4 数据一致性保障

1. **事务管理**：
```java
@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
public void executeReconciliation(ReconciliationTask task) {
    // 确保补偿操作的原子性
    updateRedisData(task);
    updateDatabaseData(task);
    updateTaskStatus(task);
}
```

2. **幂等性保证**：
```java
public void idempotentReconciliation(String reconciliationId, Runnable reconciliationTask) {
    String lockKey = "reconciliation_lock_" + reconciliationId;
    RLock lock = redissonClient.getLock(lockKey);

    try {
        if (lock.tryLock(30, TimeUnit.SECONDS)) {
            // 检查是否已经处理过
            if (!isAlreadyProcessed(reconciliationId)) {
                reconciliationTask.run();
                markAsProcessed(reconciliationId);
            }
        }
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
```

## 9. 总结

Redis与数据库记录补偿机制是保障系统数据一致性的重要组件，通过以下特点确保系统的可靠性：

### 9.1 核心特点

- **自动化补偿**：无需人工干预，自动发现和修复数据不一致
- **精确对比**：从多个维度进行数据对比，确保补偿的准确性
- **逆向还原**：通过操作记录逆向推导正确的数据状态
- **事务保证**：确保补偿操作的原子性和一致性

### 9.2 适用场景

- 高并发购票系统的数据一致性保障
- 分布式系统中的数据同步问题
- 缓存与数据库的一致性维护
- 异常恢复和数据修复场景

### 9.3 扩展建议

- 根据业务量调整定时任务的执行频率
- 增加更多的监控指标和告警机制
- 优化批量处理逻辑提高性能
- 完善错误处理和重试机制

通过这套完整的补偿机制，系统能够在高并发场景下保持数据的最终一致性，为用户提供可靠的服务体验。
