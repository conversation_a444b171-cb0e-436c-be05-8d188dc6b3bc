# ShardingSphere分库分表数据路由实现详解

## 1. 整体架构概述

### 1.1 技术选型
项目采用ShardingSphere-JDBC实现分库分表，通过YAML配置文件定义分片规则，支持多种分片策略：
- **标准分片**：单一分片键，使用MOD、HASH_MOD等算法
- **复杂分片**：多个分片键，使用自定义算法
- **广播表**：字典表等在每个库中都保存完整数据

### 1.2 数据源配置
```yaml
dataSources:
  ds_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: *************************************************
    username: root
    password: qazxsw890
  ds_1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: *************************************************
    username: root
    password: qazxsw890
```

### 1.3 分片规则配置
```yaml
rules:
  - !SHARDING
    tables:
      d_order:
        actualDataNodes: ds_${0..1}.d_order_${0..3}
        databaseStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: databaseOrderComplexGeneArithmetic
        tableStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: tableOrderComplexGeneArithmetic
```

## 2. 数据路由的核心机制

### 2.1 分片键设计原则

#### 订单表分片键设计
- **主分片键**：`order_number`（订单号）
- **辅助分片键**：`user_id`（用户ID）
- **设计原因**：
  - 订单号保证唯一性和精确查询
  - 用户ID保证同一用户数据聚合
  - 支持多维度查询需求

#### 节目表分片键设计
- **分片键**：`program_id`（节目ID）或 `id`
- **设计原因**：
  - 节目相关查询以节目ID为主
  - 座位、票档等数据与节目强关联

#### 用户表分片键设计
- **分片键**：`mobile`（手机号）、`email`（邮箱）、`id`（用户ID）
- **设计原因**：
  - 支持多种登录方式的查询
  - 保证用户数据均匀分布

### 2.2 订单号生成策略

#### 雪花算法改进版
```java
public synchronized long getOrderNumber(long userId, long tableCount) {
    long timestamp = getBase();
    long sequenceShift = log2N(tableCount);
    return ((timestamp - BASIS_TIME) << timestampLeftShift)
            | (datacenterId << datacenterIdShift)
            | (workerId << workerIdShift)
            | (sequence << sequenceShift)
            | (userId % tableCount);
}
```

#### 关键设计特点
1. **时间戳**：保证订单号的时序性
2. **机器标识**：支持分布式环境
3. **序列号**：同一毫秒内的唯一性
4. **用户标识**：最低位包含 `userId % tableCount`，确保同一用户订单路由到相同分片

### 2.3 分片算法类型

#### 标准分片算法（MOD）
```yaml
shardingAlgorithms:
  databaseProgramModModel:
    type: MOD
    props:
      sharding-count: 2
```
- **适用场景**：单一分片键，数据分布均匀
- **计算方式**：`分片键值 % 分片数量`

#### 哈希分片算法（HASH_MOD）
```yaml
shardingAlgorithms:
  databasePayHashModModel:
    type: HASH_MOD
    props:
      sharding-count: 2
```
- **适用场景**：字符串类型分片键
- **计算方式**：`hash(分片键值) % 分片数量`

#### 自定义复杂分片算法（CLASS_BASED）
```yaml
shardingAlgorithms:
  databaseOrderComplexGeneArithmetic:
    type: CLASS_BASED
    props:
      sharding-count: 2
      table-sharding-count: 4
      strategy: complex
      algorithmClassName: com.damai.shardingsphere.DatabaseOrderComplexGeneArithmetic
```

## 3. 自定义分片算法实现

### 3.1 数据库分片算法

```java
public class DatabaseOrderComplexGeneArithmetic implements ComplexKeysShardingAlgorithm<Long> {
    
    private int shardingCount;           // 数据库分片数量
    private int tableShardingCount;      // 表分片数量
    
    @Override
    public void init(Properties props) {
        this.shardingCount = Integer.parseInt(props.getProperty("sharding-count"));
        this.tableShardingCount = Integer.parseInt(props.getProperty("table-sharding-count"));
    }
    
    @Override
    public Collection<String> doSharding(Collection<String> allActualSplitDatabaseNames, 
                                       ComplexKeysShardingValue<Long> complexKeysShardingValue) {
        List<String> actualDatabaseNames = new ArrayList<>();
        Map<String, Collection<Long>> columnNameAndShardingValuesMap = 
                complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        
        // 优先使用order_number，其次使用user_id
        Collection<Long> orderNumberValues = columnNameAndShardingValuesMap.get("order_number");
        Collection<Long> userIdValues = columnNameAndShardingValuesMap.get("user_id");
        
        Long value = null;
        if (CollectionUtil.isNotEmpty(orderNumberValues)) {
            value = orderNumberValues.stream().findFirst().get();
        } else if (CollectionUtil.isNotEmpty(userIdValues)) {
            value = userIdValues.stream().findFirst().get();
        }
        
        if (Objects.nonNull(value)) {
            long databaseIndex = calculateDatabaseIndex(shardingCount, value, tableShardingCount);
            actualDatabaseNames.add("ds_" + databaseIndex);
        }
        
        return actualDatabaseNames;
    }
    
    /**
     * 基因算法：计算数据库索引
     * 通过二进制位操作实现更均匀的分布
     */
    public long calculateDatabaseIndex(Integer databaseCount, Long splicingKey, Integer tableCount) {
        String splicingKeyBinary = Long.toBinaryString(splicingKey);
        long replacementLength = log2N(tableCount);
        String geneBinaryStr = splicingKeyBinary.substring(
                splicingKeyBinary.length() - (int) replacementLength);
        
        if (StringUtil.isNotEmpty(geneBinaryStr)) {
            int h;
            int geneOptimizeHashCode = (h = geneBinaryStr.hashCode()) ^ (h >>> 16);
            return (databaseCount - 1) & geneOptimizeHashCode;
        }
        throw new DaMaiFrameException(BaseCode.NOT_FOUND_GENE);
    }
}
```

### 3.2 表分片算法

```java
public class TableOrderComplexGeneArithmetic implements ComplexKeysShardingAlgorithm<Long> {
    
    private int shardingCount;
    
    @Override
    public void init(Properties props) {
        shardingCount = Integer.parseInt(props.getProperty("sharding-count"));
    }
    
    @Override
    public Collection<String> doSharding(Collection<String> allActualSplitTableNames, 
                                       ComplexKeysShardingValue<Long> complexKeysShardingValue) {
        List<String> actualTableNames = new ArrayList<>();
        String logicTableName = complexKeysShardingValue.getLogicTableName();
        Map<String, Collection<Long>> columnNameAndShardingValuesMap = 
                complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        
        Collection<Long> orderNumberValues = columnNameAndShardingValuesMap.get("order_number");
        Collection<Long> userIdValues = columnNameAndShardingValuesMap.get("user_id");
        
        Long value = null;
        if (CollectionUtil.isNotEmpty(orderNumberValues)) {
            value = orderNumberValues.stream().findFirst().get();
        } else if (CollectionUtil.isNotEmpty(userIdValues)) {
            value = userIdValues.stream().findFirst().get();
        }
        
        if (Objects.nonNull(value)) {
            // 使用位运算进行分表路由，确保数据均匀分布
            actualTableNames.add(logicTableName + "_" + ((shardingCount - 1) & value));
        }
        
        return actualTableNames;
    }
}
```

## 4. 不同业务场景的分片策略

### 4.1 订单服务分片策略

#### 订单主表（d_order）
- **分片规模**：2库 × 4表 = 8个分片
- **分片键**：order_number + user_id
- **分片算法**：自定义复杂分片算法
- **适用场景**：支持按订单号精确查询和按用户ID范围查询

#### 购票人订单表（d_order_ticket_user）
- **分片规模**：2库 × 4表 = 8个分片
- **分片键**：order_number + user_id
- **绑定表**：与订单主表绑定，使用相同分片策略

#### 订单记录表（d_order_ticket_user_record）
- **分片规模**：2库 × 4表 = 8个分片
- **分片键**：order_number + user_id
- **用途**：记录订单状态变更历史

#### 订单节目关联表（d_order_program）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：program_id
- **分片算法**：MOD取模
- **用途**：订单与节目的关联关系

### 4.2 节目服务分片策略

#### 节目主表（d_program）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：id
- **分片算法**：MOD取模

#### 座位表（d_seat）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：program_id
- **分片算法**：MOD取模
- **业务考虑**：座位信息与节目强关联

#### 票档表（d_ticket_category）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：program_id
- **分片算法**：MOD取模

#### 节目分类表（d_program_category）
- **特殊处理**：广播表
- **说明**：在每个数据库中都保存完整数据

### 4.3 用户服务分片策略

#### 用户主表（d_user）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：id
- **分片算法**：MOD取模

#### 用户手机号表（d_user_mobile）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：mobile
- **分片算法**：HASH_MOD

#### 用户邮箱表（d_user_email）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：email
- **分片算法**：HASH_MOD

### 4.4 支付服务分片策略

#### 支付账单表（d_pay_bill）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：out_order_no
- **分片算法**：HASH_MOD

#### 退款账单表（d_refund_bill）
- **分片规模**：2库 × 2表 = 4个分片
- **分片键**：out_order_no
- **分片算法**：HASH_MOD

## 5. 数据路由执行流程

### 5.1 插入数据流程

```mermaid
graph TD
    A[业务层发起插入请求] --> B[生成包含分片信息的主键]
    B --> C[ShardingSphere拦截SQL]
    C --> D[解析SQL提取分片键]
    D --> E[调用分片算法计算目标库表]
    E --> F[路由到具体数据库执行]
    F --> G[返回执行结果]
```

#### 详细步骤
1. **主键生成**：使用UidGenerator生成包含分片信息的订单号
2. **SQL拦截**：ShardingSphere拦截MyBatis生成的SQL
3. **分片键提取**：从SQL中提取order_number和user_id
4. **算法计算**：调用自定义分片算法计算目标分片
5. **SQL路由**：将SQL路由到计算出的目标库表
6. **执行返回**：在目标库表执行SQL并返回结果

### 5.2 查询数据流程

```mermaid
graph TD
    A[业务层发起查询请求] --> B[ShardingSphere解析查询SQL]
    B --> C{分片键是否完整?}
    C -->|是| D[精确路由到单个分片]
    C -->|否| E[广播查询到多个分片]
    D --> F[执行查询]
    E --> G[并行查询多个分片]
    F --> H[返回结果]
    G --> I[合并查询结果]
    I --> H
```

#### 查询场景分析

**精确路由场景**：
```java
// 按订单号查询（包含完整分片键）
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getOrderNumber, orderNumber);
// 路由到单个分片：ds_0.d_order_1
```

**范围路由场景**：
```java
// 按用户ID查询（分片键不完整）
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getUserId, userId);
// 可能路由到多个分片进行查询
```

**广播查询场景**：
```java
// 不包含分片键的查询
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getOrderStatus, OrderStatus.PAID);
// 广播到所有分片查询
```

### 5.3 路由优化策略

#### 绑定表配置
```yaml
bindingTables:
  - d_order,d_order_ticket_user
```

**作用**：
- 确保相关联的表使用相同的分片策略
- 避免跨库JOIN操作
- 提高关联查询性能
- 保证数据一致性

#### 广播表配置
```yaml
broadcastTables:
  - d_program_category
```

**适用场景**：
- 数据量小的字典表
- 更新频率低的配置表
- 需要与分片表关联的基础数据表

## 6. 实际应用示例

### 6.1 订单创建场景

```java
@Service
public class OrderService {
    
    @Autowired
    private UidGenerator uidGenerator;
    
    @Transactional(rollbackFor = Exception.class)
    public String doCreate(OrderCreateDomain orderCreateDomain) {
        // 1. 生成包含分片信息的订单号
        // 订单号最低位包含 userId % tableCount，确保路由一致性
        Order order = new Order();
        order.setId(uidGenerator.getUid());
        order.setOrderNumber(orderCreateDomain.getOrderNumber()); // 已包含分片信息
        order.setUserId(orderCreateDomain.getUserId());
        
        // 2. ShardingSphere自动根据order_number和user_id路由到对应分片
        // 例如：路由到 ds_1.d_order_2
        orderMapper.insert(order);
        
        // 3. 相关表使用相同分片键，确保数据在同一分片
        List<OrderTicketUser> orderTicketUserList = buildOrderTicketUserList(orderCreateDomain);
        orderTicketUserService.saveBatch(orderTicketUserList);
        
        return String.valueOf(order.getOrderNumber());
    }
}
```

### 6.2 订单查询场景

```java
@Service
public class OrderService {
    
    // 精确查询：根据订单号查询（单分片路由）
    public OrderGetVo get(OrderGetDto orderGetDto) {
        LambdaQueryWrapper<Order> orderLambdaQueryWrapper =
                Wrappers.lambdaQuery(Order.class)
                        .eq(Order::getOrderNumber, orderGetDto.getOrderNumber());
        // ShardingSphere根据order_number精确路由到单个分片
        Order order = orderMapper.selectOne(orderLambdaQueryWrapper);
        
        // 绑定表查询，自动路由到相同分片
        LambdaQueryWrapper<OrderTicketUser> orderTicketUserLambdaQueryWrapper = 
                Wrappers.lambdaQuery(OrderTicketUser.class)
                        .eq(OrderTicketUser::getOrderNumber, order.getOrderNumber());
        List<OrderTicketUser> orderTicketUserList = 
                orderTicketUserMapper.selectList(orderTicketUserLambdaQueryWrapper);
        
        return buildOrderGetVo(order, orderTicketUserList);
    }
    
    // 范围查询：根据用户ID查询订单列表（可能多分片路由）
    public List<OrderListVo> selectList(OrderListDto orderListDto) {
        LambdaQueryWrapper<Order> orderLambdaQueryWrapper = 
                Wrappers.lambdaQuery(Order.class)
                        .eq(Order::getUserId, orderListDto.getUserId())
                        .orderByDesc(Order::getCreateOrderTime);
        // ShardingSphere可能路由到多个分片进行查询
        List<Order> orderList = orderMapper.selectList(orderLambdaQueryWrapper);
        
        return BeanUtil.copyToList(orderList, OrderListVo.class);
    }
}
```

### 6.3 跨服务查询场景

```java
@Service
public class ProgramOrderService {
    
    public String create(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
        // 1. 在节目服务中处理座位锁定（按program_id分片）
        // 路由到节目服务的对应分片
        List<SeatVo> purchaseSeatList = lockSeats(programOrderCreateDto);
        
        // 2. 构建订单创建参数
        OrderCreateDto orderCreateDto = buildCreateOrderParam(programOrderCreateDto, purchaseSeatList, orderVersion);
        
        // 3. 调用订单服务创建订单（按order_number+user_id分片）
        // 路由到订单服务的对应分片
        ApiResponse<String> orderResponse = orderClient.create(orderCreateDto);
        
        return orderResponse.getData();
    }
    
    private OrderCreateDto buildCreateOrderParam(ProgramOrderCreateDto programOrderCreateDto,
                                               List<SeatVo> purchaseSeatList,
                                               Integer orderVersion) {
        OrderCreateDto orderCreateDto = new OrderCreateDto();
        // 生成订单号，包含用户ID的分片信息
        orderCreateDto.setOrderNumber(uidGenerator.getOrderNumber(
                programOrderCreateDto.getUserId(), ORDER_TABLE_COUNT));
        orderCreateDto.setUserId(programOrderCreateDto.getUserId());
        orderCreateDto.setProgramId(programOrderCreateDto.getProgramId());
        
        return orderCreateDto;
    }
}
```

## 7. 性能优化策略

### 7.1 分片键选择优化

#### 选择原则
1. **查询频率**：选择查询频率最高的字段作为分片键
2. **数据分布**：确保数据能够均匀分布到各个分片
3. **业务关联**：考虑业务逻辑的关联性，避免跨分片操作
4. **扩展性**：考虑未来的扩容需求

#### 订单表分片键优化
```java
// 优化前：单一分片键
shardingColumn: order_number

// 优化后：复合分片键
shardingColumns: order_number,user_id
```

**优化效果**：
- 支持按订单号的精确查询
- 支持按用户ID的范围查询
- 避免了全表扫描

### 7.2 数据分布优化

#### 基因算法应用
```java
public long calculateDatabaseIndex(Integer databaseCount, Long splicingKey, Integer tableCount) {
    // 1. 将分片键转换为二进制
    String splicingKeyBinary = Long.toBinaryString(splicingKey);
    
    // 2. 提取基因片段（最低位的log2(tableCount)位）
    long replacementLength = log2N(tableCount);
    String geneBinaryStr = splicingKeyBinary.substring(
            splicingKeyBinary.length() - (int) replacementLength);
    
    // 3. 对基因片段进行哈希优化
    int h;
    int geneOptimizeHashCode = (h = geneBinaryStr.hashCode()) ^ (h >>> 16);
    
    // 4. 计算数据库索引
    return (databaseCount - 1) & geneOptimizeHashCode;
}
```

**优化效果**：
- 通过基因算法确保数据更均匀的分布
- 避免了简单取模可能导致的数据倾斜
- 提高了查询性能

### 7.3 绑定表优化

#### 配置示例
```yaml
bindingTables:
  - d_order,d_order_ticket_user,d_order_ticket_user_record
```

#### 优化效果
```java
// 优化前：可能产生跨库JOIN
SELECT o.*, otu.* 
FROM d_order o 
JOIN d_order_ticket_user otu ON o.order_number = otu.order_number
WHERE o.order_number = 123456;

// 优化后：同一分片内JOIN
// 由于绑定表配置，两个表在同一分片内，避免了跨库操作
```

### 7.4 广播表优化

#### 适用场景
```yaml
broadcastTables:
  - d_program_category  # 节目分类表
  - d_area             # 地区表
  - d_venue            # 场馆表
```

#### 优化效果
```java
// 关联查询不需要跨库
SELECT p.*, pc.category_name 
FROM d_program p 
JOIN d_program_category pc ON p.category_id = pc.id
WHERE p.id = 123;
// d_program_category作为广播表，在每个库都有完整数据
```

### 7.5 SQL优化建议

#### 推荐的查询模式
```java
// ✅ 推荐：包含完整分片键的查询
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getOrderNumber, orderNumber)
    .eq(Order::getUserId, userId);

// ✅ 推荐：包含主要分片键的查询
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getOrderNumber, orderNumber);

// ⚠️ 谨慎使用：只包含辅助分片键的查询
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getUserId, userId);

// ❌ 避免：不包含分片键的查询
LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
    .eq(Order::getOrderStatus, OrderStatus.PAID);
```

#### 分页查询优化
```java
// 分片环境下的分页查询
public PageResult<OrderListVo> selectPage(OrderListDto orderListDto) {
    // 1. 构建包含分片键的查询条件
    LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
            .eq(Order::getUserId, orderListDto.getUserId()) // 包含分片键
            .orderByDesc(Order::getCreateOrderTime);
    
    // 2. 使用MyBatis-Plus的分页插件
    Page<Order> page = new Page<>(orderListDto.getPageNum(), orderListDto.getPageSize());
    Page<Order> orderPage = orderMapper.selectPage(page, wrapper);
    
    // 3. 转换结果
    List<OrderListVo> orderListVos = BeanUtil.copyToList(orderPage.getRecords(), OrderListVo.class);
    
    return new PageResult<>(orderListVos, orderPage.getTotal());
}
```

## 8. 监控与运维

### 8.1 SQL监控

#### 配置SQL日志
```yaml
props:
  sql-show: true  # 显示实际执行的SQL
  sql-simple: true  # 简化SQL日志输出
```

#### 监控指标
- **路由命中率**：精确路由vs广播查询的比例
- **跨分片查询频率**：需要查询多个分片的SQL频率
- **分片数据分布**：各分片的数据量分布情况
- **查询性能**：各分片的查询响应时间

### 8.2 数据分布监控

```java
@Component
public class ShardingMonitor {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void monitorDataDistribution() {
        // 监控各分片的数据量
        Map<String, Long> shardingDataCount = new HashMap<>();
        
        // 检查订单表数据分布
        for (int db = 0; db < 2; db++) {
            for (int table = 0; table < 4; table++) {
                String shardingKey = String.format("ds_%d.d_order_%d", db, table);
                Long count = getShardingDataCount(shardingKey);
                shardingDataCount.put(shardingKey, count);
            }
        }
        
        // 分析数据分布是否均匀
        analyzeDataDistribution(shardingDataCount);
    }
    
    private void analyzeDataDistribution(Map<String, Long> shardingDataCount) {
        // 计算数据分布的标准差
        double avg = shardingDataCount.values().stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0);
        
        double variance = shardingDataCount.values().stream()
                .mapToDouble(count -> Math.pow(count - avg, 2))
                .average()
                .orElse(0.0);
        
        double standardDeviation = Math.sqrt(variance);
        
        // 如果标准差过大，说明数据分布不均匀
        if (standardDeviation > avg * 0.2) {
            log.warn("数据分布不均匀，标准差: {}, 平均值: {}", standardDeviation, avg);
            // 发送告警
            alertService.sendAlert("分片数据分布不均匀", shardingDataCount.toString());
        }
    }
}
```

### 8.3 性能监控

```java
@Component
public class ShardingPerformanceMonitor {
    
    @EventListener
    public void handleSqlExecutionEvent(SqlExecutionEvent event) {
        // 记录SQL执行时间
        long executionTime = event.getExecutionTime();
        String sql = event.getSql();
        String dataSource = event.getDataSource();
        
        // 分析慢查询
        if (executionTime > 1000) { // 超过1秒的查询
            log.warn("慢查询检测: 数据源={}, 执行时间={}ms, SQL={}", 
                    dataSource, executionTime, sql);
        }
        
        // 统计各分片的查询性能
        performanceMetrics.recordExecutionTime(dataSource, executionTime);
    }
}
```

## 9. 扩容策略

### 9.1 水平扩容

#### 扩容前规划
```yaml
# 当前配置：2库 × 4表
actualDataNodes: ds_${0..1}.d_order_${0..3}

# 扩容后配置：4库 × 4表
actualDataNodes: ds_${0..3}.d_order_${0..3}
```

#### 数据迁移策略
1. **停机迁移**：适用于业务可接受短时间停机
2. **在线迁移**：使用双写+数据同步的方式
3. **分批迁移**：按时间范围分批迁移历史数据

### 9.2 垂直扩容

#### 表拆分策略
```yaml
# 拆分前：订单表包含所有字段
d_order: order_id, user_id, program_id, order_status, create_time, ...

# 拆分后：按访问频率拆分
d_order_basic: order_id, user_id, program_id, order_status, create_time
d_order_detail: order_id, order_price, distribution_mode, take_ticket_mode, ...
```

## 10. 最佳实践总结

### 10.1 设计原则
1. **业务优先**：分片策略要符合业务查询模式
2. **性能导向**：优化高频查询的路由效率
3. **扩展性**：考虑未来的扩容需求
4. **一致性**：相关表使用一致的分片策略

### 10.2 开发规范
1. **分片键必须**：所有查询尽量包含分片键
2. **避免跨分片**：减少跨分片的JOIN和事务
3. **合理分页**：分片环境下谨慎使用分页查询
4. **监控告警**：建立完善的监控和告警机制

### 10.3 运维建议
1. **定期监控**：监控数据分布和查询性能
2. **容量规划**：根据业务增长预估扩容时间
3. **备份策略**：制定分片环境下的备份恢复策略
4. **应急预案**：准备分片故障的应急处理方案

通过以上详细的分析和实践，ShardingSphere分库分表方案能够有效支撑大麦网这样的高并发业务场景，在保证性能的同时维护了数据的一致性和系统的可扩展性。
