<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-redisson-service-framework</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>damai-repeat-execute-limit-framework</artifactId>

    <name>damai-repeat-execute-limit-framework</name>
    <description>防重复执行幂等性工具</description>

    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-redisson-common-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-lock-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
