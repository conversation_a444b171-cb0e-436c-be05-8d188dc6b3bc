<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai_pro</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>damai-redisson-framework</artifactId>
    <name>damai-redisson-framework</name>
    <description>redisson封装</description>
    <packaging>pom</packaging>

    <modules>
        <module>damai-service-delay-queue-framework</module>
        <module>damai-redisson-service-framework</module>
    </modules>

</project>
