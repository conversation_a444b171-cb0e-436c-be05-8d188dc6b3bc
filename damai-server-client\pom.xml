<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai_pro</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>damai-server-client</artifactId>
    <packaging>pom</packaging>

    <name>server-client</name>
    <description>服务feign接口</description>

    <modules>
        <module>damai-order-client</module>
        <module>damai-base-data-client</module>
        <module>damai-user-client</module>
        <module>damai-job-client</module>
        <module>damai-customize-client</module>
        <module>damai-program-client</module>
        <module>damai-pay-client</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-common</artifactId>
            <version>${revision}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
    </dependencies>
</project>
