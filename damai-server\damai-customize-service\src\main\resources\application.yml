#服务端口
server:
  port: 6084
# 应用名称
spring:
  profiles:
    active: local
  application:
    name: ${prefix.distinction.name:dam<PERSON>}-customize-service
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    servlet:
      load-on-startup: 1
    jackson:
      time-zone: GMT+8
      date-format: yyyy-MM-dd HH:mm:ss
      generator:
        WRITE_NUMBERS_AS_STRINGS: true
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 3000
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
  kafka:
    bootstrap-servers: 127.0.0.1:9092
    consumer:
      #默认的消费组ID
      group-id: api_data
      #是否自动提交offset
      enable-auto-commit: true
      #提交offset延时
      auto-commit-interval: 2000
      # 当kafka中没有初始offset或offset超出范围时将自动重置offset
      # earliest:重置为分区中最小的offset;
      # latest:重置为分区中最新的offset(消费分区中新产生的数据);
      # none:只要有一个分区不存在已提交的offset,就抛出异常;
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 主题
      topic: save_api_data   
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      logic-delete-field: status
      logic-delete-value: 0
      logic-not-delete-value: 1
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    local-cache-scope: statement
feign:
  sentinel:
    enabled: false
  hystrix:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 512
    response:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
    health:
      show-details: always
  security:
    enabled: false
  health:
    elasticsearch:
      enabled: false
jasypt:
  encryptor:
    password: bgtjkjl!%^sdc
    algorithm: PBEWithMD5AndDES
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      #生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: com.damai.controller
#knife4j配置
knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: false
  #是否启用登录认证
  basic:
    enable: false
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true