#服务端口
server:
  port: 6085

  http2:
    enabled: false
# 应用名称
spring:
  application:
    name: ${prefix.distinction.name:damai}-gateway-service
  profiles:
    # 微服务调用
    active: pro
    # 单体服务调用
#    active: single
  main:
    allow-bean-definition-overriding: true
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 3000
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        metadata:
          # true表示灰度服务/false表示生产服务(默认)
          gray: false
    sentinel:
      eager: true
      transport:
        port: 8768
        dashboard: 127.0.0.1:8082
#      datasource:
#        degrade:
#          nacos:
#            server-addr: ${spring.cloud.nacos.discovery.server-addr}
#            dataId: ${spring.application.name}-degrade-rules
#            groupId: SENTINEL_GROUP
#            dataType: json
#            rule-type: degrade
#        flow:
#          nacos:
#            server-addr: ${spring.cloud.nacos.discovery.server-addr}
#            dataId: ${spring.application.name}-flow-rules
#            groupId: SENTINEL_GROUP
#            dataType: json
#            rule-type: flow
    gateway:
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials
  kafka:
    bootstrap-servers: 127.0.0.1:9092
    producer:
      retries: 1
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    topic: save_api_data
api:
  limit:
    paths: /**/customize/test/test   
feign:
  sentinel:
    enabled: false
  hystrix:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 512
    response:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
    health:
      show-details: always
  security:
    enabled: false
  health:
    elasticsearch:
      enabled: false
jasypt:
  encryptor:
    password: bgtjkjl!%^sdc
    algorithm: PBEWithMD5AndDES
# knife4j 网关聚合
knife4j:
  gateway:
    enabled: true
    # 指定服务发现的模式聚合微服务文档，并且是默认 default 分组
    strategy: discover
    discover:
      # OpenAPI 3.0 规范 
      version: openapi3
      enabled: true
