dataSources:
  ds_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **********************************************************************************************************************************************************************************************
    username: root
    password: qazxsw890
    hikari:
      max-lifetime: 60000
  ds_1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **********************************************************************************************************************************************************************************************
    username: root
    password: qazxsw890
    hikari:
      max-lifetime: 60000

rules:
  - !SHARDING
    tables:
      d_order:
        actualDataNodes: ds_${0..1}.d_order_${0..3}
        databaseStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: databaseOrderComplexGeneArithmetic
        tableStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: tableOrderComplexGeneArithmetic
      d_order_ticket_user:
        actualDataNodes: ds_${0..1}.d_order_ticket_user_${0..3}
        databaseStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: databaseOrderTicketUserComplexGeneArithmetic
        tableStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: tableOrderTicketUserComplexGeneArithmetic
      d_order_ticket_user_record:
        actualDataNodes: ds_${0..1}.d_order_ticket_user_record_${0..3}
        databaseStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: databaseOrderTicketUserRecordComplexGeneArithmetic
        tableStrategy:
          complex:
            shardingColumns: order_number,user_id
            shardingAlgorithmName: tableOrderTicketUserRecordComplexGeneArithmetic
      d_order_program:
        actualDataNodes: ds_${0..1}.d_order_program_${0..1}
        databaseStrategy:
          standard:
            shardingColumn: program_id
            shardingAlgorithmName: databaseOrderProgramModModel
        tableStrategy:
          standard:
            shardingColumn: program_id
            shardingAlgorithmName: tableOrderProgramModModel
    bindingTables:
      - d_order,d_order_ticket_user
    shardingAlgorithms:
      databaseOrderComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 2
          table-sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.DatabaseOrderComplexGeneArithmetic
      tableOrderComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.TableOrderComplexGeneArithmetic
      databaseOrderTicketUserComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 2
          table-sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.DatabaseOrderComplexGeneArithmetic
      tableOrderTicketUserComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.TableOrderComplexGeneArithmetic
      databaseOrderTicketUserRecordComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 2
          table-sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.DatabaseOrderComplexGeneArithmetic
      tableOrderTicketUserRecordComplexGeneArithmetic:
        type: CLASS_BASED
        props:
          sharding-count: 4
          strategy: complex
          algorithmClassName: com.damai.shardingsphere.TableOrderComplexGeneArithmetic
      databaseOrderProgramModModel:
        type: MOD
        props:
          sharding-count: 2
      tableOrderProgramModModel:
        type: MOD
        props:
          sharding-count: 2
props:
  sql-show: true