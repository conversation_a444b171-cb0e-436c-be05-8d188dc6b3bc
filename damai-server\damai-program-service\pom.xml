<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-server</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>damai-program-service</artifactId>

    <name>program-service</name>
    <description>演出节目服务</description>

    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--去除spring boot自带的 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-initialize</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-redis-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-redis-stream-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-lock-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-bloom-filter-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-repeat-execute-limit-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-delay-queue-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-program-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-base-data-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-order-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-component</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- nacos start -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-id-generator-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin-starter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-thread-pool-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-elasticsearch-framework</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <version>4.0.9.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>transport</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-gray-transition-webmvc-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>${opencsv.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.damai.ProgramApplication</mainClass>
                    <!-- 如果以jar包启动需要将此配置去掉 -->
                    <!--<skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
