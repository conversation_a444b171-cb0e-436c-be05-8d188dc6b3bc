<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.damai.mapper.ProgramMapper">
    
    <select id="selectHomeList" parameterType="com.damai.dto.ProgramListDto" resultType="com.damai.entity.Program">
        <if test = 'programListDto.parentProgramCategoryIds != null and programListDto.parentProgramCategoryIds.size>0'>
            <foreach collection='programListDto.parentProgramCategoryIds' item='parentProgramCategoryId' index='index' separator=' union all '>
                select * from (
                    select
                        dp.id,dp.area_id,dp.program_category_id,dp.parent_program_category_id,dp.title,dp.actor,
                        dp.place,dp.item_picture
                    from
                        d_program dp
                    where
                        dp.program_status = 1
                    <if test ='programListDto.areaId == null'>
                        and dp.prime = 1
                    </if>
                    <if test ='programListDto.areaId != null'>
                        and dp.area_id = #{programListDto.areaId,jdbcType=BIGINT}
                    </if>
                    and
                        dp.parent_program_category_id = #{parentProgramCategoryId,jdbcType=BIGINT} limit 7
                ) as tmp
            </foreach>
        </if>
    </select>
    
    <select id="selectPage" parameterType="com.damai.dto.ProgramPageListDto" resultType="com.damai.entity.ProgramJoinShowTime">
        select
            dp.id,dp.area_id,dp.program_category_id,dp.parent_program_category_id,dp.title,dp.actor,
            dp.place,dp.item_picture,ds.show_time,ds.show_week_time,ds.show_day_time
        from 
            d_program dp left join d_program_show_time ds
        on dp.id = ds.program_id
        where 
            dp.status = 1 and ds.status = 1 and dp.program_status = 1
            <if test ='programPageListDto.areaId == null'>
                and dp.prime = 1
            </if>
            <if test ='programPageListDto.areaId != null'>
             and dp.area_id = #{programPageListDto.areaId,jdbcType=BIGINT} 
            </if>
            <if test = 'programPageListDto.programCategoryId != null'>
                and dp.program_category_id = #{programPageListDto.programCategoryId,jdbcType=BIGINT}
            </if>
            <if test = 'programPageListDto.parentProgramCategoryId != null'>
                and dp.parent_program_category_id = #{programPageListDto.parentProgramCategoryId,jdbcType=BIGINT}
            </if>
            <if test = 'programPageListDto.startDateTime != null'>
              and ds.show_day_time &gt;= #{programPageListDto.startDateTime}
            </if>
            <if test = 'programPageListDto.endDateTime != null'>
              and ds.show_day_time &lt;= #{programPageListDto.endDateTime}
            </if>
        <if test="programPageListDto.type == 2">
            order by dp.high_heat desc
        </if>
        <if test="programPageListDto.type == 3">
            order by ds.show_time asc
        </if>
        <if test="programPageListDto.type == 4">
            order by dp.issue_time asc
        </if>
    </select>
</mapper>