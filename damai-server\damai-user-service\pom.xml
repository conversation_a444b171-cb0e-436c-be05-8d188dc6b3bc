<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-server</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>damai-user-service</artifactId>

    <name>user-service</name>
    <description>用户服务</description>
    
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--去除spring boot自带的 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-initialize</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-lock-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-bloom-filter-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-user-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-base-data-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-service-component</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-id-generator-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-redis-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <!-- nacos start -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-captcha-framework</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin-starter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.example</groupId>-->
<!--            <artifactId>damai-service-gray-transition-webmvc-framework</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>${activation.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>damai-thread-pool-framework</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.damai.UserApplication</mainClass>
                    <!-- 如果以jar包启动需要将此配置去掉 -->
                    <!--<skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
