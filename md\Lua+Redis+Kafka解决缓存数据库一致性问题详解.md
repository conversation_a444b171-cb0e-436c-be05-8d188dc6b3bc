# Lua+Redis+Kafka解决缓存和数据库不一致问题详解

## 项目背景

在大麦订票系统中，面临着高并发购票场景下的核心挑战：**如何保证缓存和数据库的数据一致性，同时防止余票超卖问题**。项目通过Lua脚本+Redis原子性操作结合Kafka消息队列的方式，构建了一套完整的解决方案。

## 核心设计思想

### 1. 问题分析
- **高并发冲突**：多用户同时购买同一座位或票档
- **数据一致性**：缓存中的余票数量与数据库不一致
- **超卖风险**：库存扣减不原子性导致超卖
- **性能要求**：需要在保证一致性的同时维持高性能

### 2. 解决策略
- **Lua脚本保证原子性**：所有Redis操作在一个原子事务中完成
- **Kafka异步处理**：通过消息队列异步同步数据库
- **多级验证机制**：缓存验证+数据库验证双重保障
- **补偿机制**：失败场景下的数据回滚和补偿

## 核心业务流程

### 1. 购票流程 - 防止超卖的核心实现

#### 1.1 Lua脚本原子性操作
```lua
-- programDataCreateOrderResolution.lua
-- 核心防超卖逻辑

-- 验证余票数量
local remain_number_str = redis.call('hget', ticket_remain_number_hash_key, tostring(ticket_category_id))
if not remain_number_str then
    return string.format('{"%s": %d}', 'code', 40010)  -- 票档不存在
end
local remain_number = tonumber(remain_number_str)
if (count > remain_number) then
    return string.format('{"%s": %d}', 'code', 40011)  -- 余票不足
end

-- 验证座位状态
local seat_vo_str = redis.call('hget', seat_no_sold_hash_key, tostring(id))
if not seat_vo_str then
    return string.format('{"%s": %d}', 'code', 40001)  -- 座位不存在
end
local seat_vo = cjson.decode(seat_vo_str)
if (seat_vo.sellStatus == 2) then
    return string.format('{"%s": %d}', 'code', 40002)  -- 座位已锁定
end
if (seat_vo.sellStatus == 3) then
    return string.format('{"%s": %d}', 'code', 40003)  -- 座位已售出
end

-- 原子性扣减库存
redis.call('hincrby', ticket_remain_number_hash_key, ticket_category_id, "-" .. count)

-- 原子性更新座位状态
redis.call('hdel', seat_no_sold_hash_key, unpack(seat_id_array))  -- 从未售卖中删除
redis.call('hmset', seat_lock_hash_key, unpack(seat_data_array))  -- 添加到锁定中
```

#### 1.2 Java层面的调用
```java
@Component
public class ProgramCacheCreateOrderResolutionOperate {
    
    @Autowired
    private RedisCache redisCache;
    
    private DefaultRedisScript<String> redisScript;
    
    @PostConstruct
    public void init() {
        redisScript = new DefaultRedisScript<>();
        redisScript.setScriptSource(new ResourceScriptSource(
            new ClassPathResource("lua/programDataCreateOrderResolution.lua")));
        redisScript.setResultType(String.class);
    }
    
    public String programCacheCreateOrderOperate(List<String> keys, String[] args) {
        return redisCache.getInstance().execute(redisScript, keys, args);
    }
}
```

### 2. Kafka异步处理机制

#### 2.1 订单创建消息发送
```java
@Component
public class CreateOrderSend {
    
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    public void sendMessage(String message, 
                          SuccessCallback<SendResult<String, String>> successCallback, 
                          FailureCallback failureCallback) {
        log.info("创建订单kafka发送消息 消息体 : {}", message);
        CompletableFuture<SendResult<String, String>> completableFuture = 
                kafkaTemplate.send(SpringUtil.getPrefixDistinctionName() + "-" + kafkaTopic.getTopic(), message);
        
        completableFuture.whenComplete((result, ex) -> {
            if (Objects.isNull(ex)) {
                successCallback.onSuccess(result);
            } else {
                failureCallback.onFailure(ex);
            }
        });
    }
}
```

#### 2.2 订单创建消息消费
```java
@Component
public class CreateOrderConsumer {
    
    @KafkaListener(topics = {SPRING_INJECT_PREFIX_DISTINCTION_NAME + "-" + "${spring.kafka.topic:create_order}"})
    public void consumerOrderMessage(ConsumerRecord<String, String> consumerRecord) {
        try {
            Optional.ofNullable(consumerRecord.value()).map(String::valueOf).ifPresent(value -> {
                OrderCreateMq orderCreateMq = JSON.parseObject(value, OrderCreateMq.class);
                
                long createOrderTimeTimestamp = orderCreateMq.getCreateOrderTime().getTime();
                long currentTimeTimestamp = System.currentTimeMillis();
                long delayTime = currentTimeTimestamp - createOrderTimeTimestamp;
                
                // 消息延迟检查 - 防止过期消息处理
                if (currentTimeTimestamp - createOrderTimeTimestamp > MESSAGE_DELAY_TIME) {
                    log.info("消费到kafka的创建订单消息延迟时间大于了 {} 毫秒 此订单消息被丢弃", delayTime);
                    // 将延迟丢弃的订单放入Redis中进行补偿处理
                    redisCache.leftPushForList(
                        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
                        new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));
                } else {
                    // 正常处理订单创建
                    String orderNumber = orderService.createMq(orderCreateMq);
                    log.info("消费到kafka的创建订单消息 创建订单成功 订单号 : {}", orderNumber);
                }
            });
        } catch (Exception e) {
            log.error("处理消费到kafka的创建订单消息失败 error", e);
        }
    }
}
```

### 3. 数据库同步机制

#### 3.1 订单创建时的数据库操作
```java
@Transactional(rollbackFor = Exception.class)
public String createMq(OrderCreateMq orderCreateMq) {
    // 1. 先调用节目服务更新座位状态和扣减库存
    ReduceRemainNumberDto reduceRemainNumberDto = new ReduceRemainNumberDto();
    reduceRemainNumberDto.setProgramId(orderCreateMq.getProgramId());
    reduceRemainNumberDto.setSellStatus(SellStatus.LOCK.getCode());
    reduceRemainNumberDto.setSeatIdList(seatIdList);
    reduceRemainNumberDto.setTicketCategoryCountDtoList(ticketCountList);
    
    ApiResponse<Boolean> programApiResponse = 
        programClient.operateSeatLockAndTicketCategoryRemainNumber(reduceRemainNumberDto);
    
    if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
        // 失败时将订单放入丢弃队列进行补偿
        redisCache.leftPushForList(
            RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
            new DiscardOrder(orderCreateMq, DiscardOrderReason.MODIFY_PROGRAM_REMAIN_NUMBER_SEAT_FAIL.getCode()));
        throw new DaMaiFrameException(programApiResponse);
    }
    
    // 2. 创建订单记录
    String orderNumber = createByMq(orderCreateMq);
    
    // 3. 设置订单创建成功标识
    redisCache.set(RedisKeyBuild.createRedisKey(RedisKeyManage.ORDER_MQ, orderNumber), 
                  orderNumber, 1, TimeUnit.MINUTES);
    
    return orderNumber;
}
```

#### 3.2 数据库层面的原子性操作
```java
@Transactional(rollbackFor = Exception.class)
public Boolean operateSeatLockAndTicketCategoryRemainNumber(ReduceRemainNumberDto reduceRemainNumberDto) {
    // 1. 验证座位状态
    List<Seat> seatList = seatMapper.selectList(seatLambdaQueryWrapper);
    if (CollectionUtil.isEmpty(seatList)) {
        throw new DaMaiFrameException(BaseCode.SEAT_NOT_EXIST);
    }
    
    // 2. 原子性更新座位状态
    LambdaUpdateWrapper<Seat> seatLambdaUpdateWrapper = 
        Wrappers.lambdaUpdate(Seat.class)
                .eq(Seat::getProgramId, reduceRemainNumberDto.getProgramId())
                .in(Seat::getId, seatIdList);
    Seat updateSeat = new Seat();
    updateSeat.setSellStatus(reduceRemainNumberDto.getSellStatus());
    seatMapper.update(updateSeat, seatLambdaUpdateWrapper);
    
    // 3. 原子性扣减余票数量
    for (TicketCategoryCountDto ticketCategoryCountDto : ticketCategoryCountDtoList) {
        updateRemainNumberCount += ticketCategoryMapper.reduceRemainNumber(
            ticketCategoryCountDto.getCount(), 
            ticketCategoryCountDto.getTicketCategoryId(),
            reduceRemainNumberDto.getProgramId());
    }
    
    return updateRemainNumberCount == ticketCategoryCountDtoList.size();
}
```

## 订单状态变更的一致性处理

### 1. 订单取消场景

#### 1.1 Lua脚本处理订单取消
```lua
-- OrderProgramDataResolution.lua
-- 订单取消时的库存恢复

-- 如果是将订单取消 (operate_order_status == 2)
if (operate_order_status == 2) then
    -- 恢复库存
    for index, increase_data in ipairs(ticket_category_list) do
        local program_ticket_remain_number_hash_key = increase_data.programTicketRemainNumberHashKey
        local ticket_category_id_str = increase_data.ticketCategoryId
        local increase_count = increase_data.count
        
        -- 原子性增加库存
        redis.call('HINCRBY', program_ticket_remain_number_hash_key, ticket_category_id_str, increase_count)
        
        -- 更新记录中的数量变化
        for index2, ticket_category_record in ipairs(ticket_category_record_list) do
            if tonumber(ticket_category_id_str) == ticket_category_record.ticketCategoryId then
                ticket_category_record.afterAmount = ticket_category_record.beforeAmount + increase_count
                ticket_category_record.changeAmount = increase_count
            end
        end
    end
end

-- 记录操作流水
local purchase_record = {
    recordType = record_type,
    timestamp = currentTimeMillis,
    ticketCategoryRecordList = ticket_category_record_list
}
redis.call('hset', string.format(record_hash_key, program_id), identifier_id, cjson.encode(purchase_record))
```

#### 1.2 Java层面的订单取消处理
```java
@ServiceLock(name = ORDER_CANCEL_LOCK, keys = {"#orderCancelDto.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public boolean cancel(OrderCancelDto orderCancelDto) {
    // 更新订单相关数据
    updateOrderRelatedData(orderCancelDto.getOrderNumber(), OrderStatus.CANCEL);
    return true;
}

private void updateOrderRelatedData(String orderNumber, OrderStatus orderStatus) {
    // 1. 更新订单状态
    Order order = orderMapper.selectOne(Wrappers.lambdaQuery(Order.class)
                                      .eq(Order::getOrderNumber, orderNumber));
    
    // 2. 更新购票人订单状态
    List<OrderTicketUser> orderTicketUserList = orderTicketUserMapper.selectList(
        Wrappers.lambdaQuery(OrderTicketUser.class).eq(OrderTicketUser::getOrderNumber, orderNumber));
    
    // 3. 如果是取消操作，减少用户订单计数
    if (Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {
        redisCache.incrBy(RedisKeyBuild.createRedisKey(
            RedisKeyManage.ACCOUNT_ORDER_COUNT, order.getUserId(), order.getProgramId()), 
            -updateTicketUserOrderResult);
    }
    
    // 4. 更新缓存和节目库相关数据
    updateProgramRelatedDataResolution(programId, seatMap, orderStatus,
                                     order.getIdentifierId(), order.getUserId(),
                                     seatIdAndTicketUserIdDomainList, order.getOrderVersion());
}
```

### 2. 订单支付成功场景

#### 2.1 支付成功的状态更新
```java
private void updateProgramRelatedDataResolution(Long programId, Map<Long, List<Long>> seatMap,
                                              OrderStatus orderStatus, Long identifierId,
                                              Long userId, List<SeatIdAndTicketUserIdDomain> seatIdAndTicketUserIdDomainList,
                                              Integer orderVersion) {

    // 构建座位数据更新参数
    JSONArray addSeatDatajsonArray = new JSONArray();
    seatMap.forEach((k, v) -> {
        JSONObject seatDatajsonObject = new JSONObject();
        String seatHashKeyAdd = "";

        // 根据订单状态确定座位存储位置
        if (Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {
            // 取消订单：座位回到未售卖状态
            seatHashKeyAdd = RedisKeyBuild.createRedisKey(
                RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH, programId, k).getRelKey();
            for (SeatVo seatVo : v) {
                seatVo.setSellStatus(SellStatus.NO_SOLD.getCode());
            }
        } else if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
            // 支付成功：座位变为已售卖状态
            seatHashKeyAdd = RedisKeyBuild.createRedisKey(
                RedisKeyManage.PROGRAM_SEAT_SOLD_RESOLUTION_HASH, programId, k).getRelKey();
            for (SeatVo seatVo : v) {
                seatVo.setSellStatus(SellStatus.SOLD.getCode());
            }
        }

        // 构建座位数据数组
        JSONArray seatDataArray = new JSONArray();
        for (SeatVo seatVo : v) {
            seatDataArray.add(String.valueOf(seatVo.getId()));
            seatDataArray.add(JSON.toJSONString(seatVo));
        }

        seatDatajsonObject.put("seatHashKeyAdd", seatHashKeyAdd);
        seatDatajsonObject.put("seatDataList", seatDataArray);
        addSeatDatajsonArray.add(seatDatajsonObject);
    });

    // 执行Lua脚本进行原子性更新
    String recordType = Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode()) ?
            RecordType.INCREASE.getValue() : RecordType.CHANGE_STATUS.getValue();

    List<String> keys = Arrays.asList(
        String.valueOf(orderStatus.getCode()),
        String.valueOf(programId),
        RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, "%s").getRelKey(),
        recordType + GLIDE_LINE + identifierId + GLIDE_LINE + userId,
        recordType
    );

    Object[] data = new String[4];
    data[0] = JSON.toJSONString(unLockSeatIdjsonArray);  // 解锁座位数据
    data[1] = JSON.toJSONString(addSeatDatajsonArray);   // 添加座位数据
    data[2] = JSON.toJSONString(jsonArray);              // 库存变更数据
    data[3] = JSON.toJSONString(seatIdAndTicketUserIdDomainList); // 座位购票人映射

    // 根据订单版本选择不同的处理方式
    if (!orderVersion.equals(ProgramOrderVersion.V4_VERSION.getValue())) {
        // V1-V3版本：先更新缓存，再异步更新数据库
        orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
        if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
            programOperateDataDto.setSellStatus(SellStatus.SOLD.getCode());
            delayOperateProgramDataSend.sendMessage(JSON.toJSONString(programOperateDataDto));
        }
    } else {
        // V4版本：先更新数据库，再更新缓存
        if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode()) ||
            Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {

            programOperateDataDto.setSellStatus(
                Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())
                    ? SellStatus.SOLD.getCode() : SellStatus.NO_SOLD.getCode());

            ApiResponse<Boolean> programApiResponse = programClient.operateProgramData(programOperateDataDto);
            if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                throw new DaMaiFrameException(programApiResponse);
            }
        }
        // 执行Lua脚本更新缓存
        orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
    }
}
```

## 数据一致性保障机制

### 1. 多级验证机制

#### 1.1 缓存层验证
```lua
-- 第一层：Redis缓存验证
local remain_number_str = redis.call('hget', ticket_remain_number_hash_key, tostring(ticket_category_id))
if not remain_number_str then
    return string.format('{"%s": %d}', 'code', 40010)  -- 票档不存在
end

local remain_number = tonumber(remain_number_str)
if (count > remain_number) then
    return string.format('{"%s": %d}', 'code', 40011)  -- 余票不足
end
```

#### 1.2 数据库层验证
```java
// 第二层：数据库验证
@Transactional(rollbackFor = Exception.class)
public Boolean operateSeatLockAndTicketCategoryRemainNumber(ReduceRemainNumberDto reduceRemainNumberDto) {
    // 验证座位状态
    List<Seat> seatList = seatMapper.selectList(seatLambdaQueryWrapper);
    for (Seat seat : seatList) {
        if (!Objects.equals(seat.getSellStatus(), SellStatus.NO_SOLD.getCode())) {
            throw new DaMaiFrameException(BaseCode.SEAT_SELL_STATUS_NOT_CORRECT);
        }
    }

    // 验证票档余票数量
    for (TicketCategoryCountDto ticketCategoryCountDto : ticketCategoryCountDtoList) {
        TicketCategory ticketCategory = ticketCategoryMapper.selectOne(
            Wrappers.lambdaQuery(TicketCategory.class)
                   .eq(TicketCategory::getId, ticketCategoryCountDto.getTicketCategoryId())
                   .eq(TicketCategory::getProgramId, reduceRemainNumberDto.getProgramId()));

        if (Objects.isNull(ticketCategory)) {
            throw new DaMaiFrameException(BaseCode.TICKET_CATEGORY_NOT_EXIST);
        }

        if (ticketCategory.getRemainNumber() < ticketCategoryCountDto.getCount()) {
            throw new DaMaiFrameException(BaseCode.TICKET_CATEGORY_REMAIN_NUMBER_NOT_ENOUGH);
        }
    }

    return true;
}
```

### 2. 补偿机制

#### 2.1 失败订单的补偿处理
```java
// 消息延迟导致的订单丢弃
if (currentTimeTimestamp - createOrderTimeTimestamp > MESSAGE_DELAY_TIME) {
    redisCache.leftPushForList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
        new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));
}

// 数据库操作失败导致的订单丢弃
if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
    redisCache.leftPushForList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
        new DiscardOrder(orderCreateMq, DiscardOrderReason.MODIFY_PROGRAM_REMAIN_NUMBER_SEAT_FAIL.getCode()));
    throw new DaMaiFrameException(programApiResponse);
}
```

#### 2.2 数据对账机制
```java
@Component
public class ProgramRecordHandler {

    public void completeRecord(Long programId) {
        // 获取Redis中的记录和数据库中的记录进行对比
        Map<String, String> completeRedisCordMap = redisCache.getHashAll(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId));

        Map<String, String> totalProgramRecordMap = redisCache.getHashAll(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId));

        // 对账完成后更新数据库状态
        Set<String> keyList = new HashSet<>();
        addKeyList(keyList, completeRedisCordMap);
        addKeyList(keyList, totalProgramRecordMap);

        for (final String key : keyList) {
            String[] split = SplitUtil.toSplit(key);
            Long identifierId = Long.valueOf(split[0]);
            Long userId = Long.valueOf(split[1]);

            int result = updateDbOrderTicketUserRecordStatus(programId, identifierId, userId,
                                                           ReconciliationStatus.RECONCILIATION_SUCCESS);
            log.info("修改数据库记录流水成功, programId:{}, identifierId:{}, userId:{}, result:{}",
                    programId, identifierId, userId, result);
        }

        // 清理已对账的记录
        if (CollectionUtil.isNotEmpty(totalProgramRecordMap)) {
            redisCache.delForHash(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId),
                totalProgramRecordMap.keySet());
        }
    }
}
```

## 防止超卖的关键技术点

### 1. 原子性操作保障
- **Lua脚本原子性**：所有Redis操作在单个原子事务中执行
- **数据库事务**：使用@Transactional确保数据库操作的原子性
- **分布式锁**：通过Redisson分布式锁防止并发冲突

### 2. 库存扣减策略
- **预扣减机制**：先在缓存中扣减，再在数据库中确认
- **双重验证**：缓存验证+数据库验证
- **乐观锁机制**：通过版本号或时间戳防止并发更新

### 3. 异常处理机制
- **回滚机制**：操作失败时自动回滚已执行的操作
- **补偿队列**：失败的订单进入补偿队列进行后续处理
- **监控告警**：实时监控库存异常和数据不一致情况

## 总结

通过Lua+Redis+Kafka的组合方案，项目成功解决了高并发场景下的缓存和数据库一致性问题：

1. **Lua脚本**确保了Redis操作的原子性，防止了缓存层面的数据竞争
2. **Kafka消息队列**实现了缓存和数据库的异步同步，提高了系统性能
3. **多级验证机制**提供了多重保障，确保数据的最终一致性
4. **补偿机制**处理了各种异常场景，保证了系统的健壮性

这套方案在保证数据一致性的同时，最大限度地避免了余票超卖问题，为高并发购票系统提供了可靠的技术保障。
