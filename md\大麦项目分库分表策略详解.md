# 大麦项目分库分表策略详解

## 项目背景

大麦订票系统作为高并发的票务平台，面临着海量数据存储和高并发访问的挑战。随着用户量和订单量的快速增长，单库单表的架构已无法满足性能要求。因此，项目采用了基于ShardingSphere的分库分表策略，实现数据的水平拆分，提升系统的并发处理能力和存储容量。

## 一、分库分表概述

### 1.1 分库分表类型

大麦项目采用的是**水平分库分表**策略：

#### 水平分库分表（Horizontal Sharding）
- **定义**：将同一张表的数据按照某种规则分散到多个数据库的多张表中
- **特点**：表结构相同，数据不同
- **优势**：
  - 分散数据库压力，提高并发能力
  - 突破单表数据量限制
  - 提升查询性能
  - 支持线性扩展

#### 为什么不采用垂直分库分表？
- **垂直分库**：按业务模块拆分，大麦项目已通过微服务架构实现
- **垂直分表**：按字段拆分，不适合大麦的业务场景

### 1.2 技术选型

**ShardingSphere-JDBC**：
- **轻量级**：以jar包形式提供服务，无额外部署成本
- **性能高**：直连数据库，性能损耗极低
- **兼容性好**：完全兼容JDBC和各种ORM框架
- **功能丰富**：支持分库分表、读写分离、数据加密等

## 二、分库分表架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Spring Boot)                      │
├─────────────────────────────────────────────────────────────┤
│                ShardingSphere-JDBC                         │
├─────────────────────────────────────────────────────────────┤
│  用户服务     │  节目服务     │  订单服务     │  支付服务      │
│ damai_user_0  │ damai_program_0│ damai_order_0 │ damai_pay_0   │
│ damai_user_1  │ damai_program_1│ damai_order_1 │ damai_pay_1   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据库分布

#### 按服务划分的数据库
1. **用户服务**：`damai_user_0`、`damai_user_1`
2. **节目服务**：`damai_program_0`、`damai_program_1`
3. **订单服务**：`damai_order_0`、`damai_order_1`
4. **支付服务**：`damai_pay_0`、`damai_pay_1`

#### 分片规模
- **数据库数量**：每个服务2个数据库
- **表数量**：根据业务特点，2-4张表不等
- **总分片数**：数据库数 × 表数

## 三、具体分库分表策略

### 3.1 用户服务分库分表

#### 数据源配置
```yaml
dataSources:
  ds_0:
    jdbcUrl: ************************************************
  ds_1:
    jdbcUrl: ************************************************
```

#### 分片策略

##### 用户表（d_user）
```yaml
d_user:
  actualDataNodes: ds_${0..1}.d_user_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: id
      shardingAlgorithmName: databaseUserModModel
  tableStrategy:
    standard:
      shardingColumn: id
      shardingAlgorithmName: tableUserModModel
```

**分片键**：`id`（用户ID）
**分片算法**：MOD取模算法
**分片规则**：
- 数据库选择：`id % 2`
- 表选择：`id % 2`
- 最终路由：`ds_{id%2}.d_user_{id%2}`

##### 用户手机号表（d_user_mobile）
```yaml
d_user_mobile:
  actualDataNodes: ds_${0..1}.d_user_mobile_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: mobile
      shardingAlgorithmName: databaseUserMobileHashModModel
  tableStrategy:
    standard:
      shardingColumn: mobile
      shardingAlgorithmName: tableUserMobileHashMod
```

**分片键**：`mobile`（手机号）
**分片算法**：HASH_MOD哈希取模算法
**优势**：手机号分布更均匀，避免数据倾斜

##### 购票人表（d_ticket_user）
```yaml
d_ticket_user:
  actualDataNodes: ds_${0..1}.d_ticket_user_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: user_id
      shardingAlgorithmName: databaseTicketUserModModel
  tableStrategy:
    standard:
      shardingColumn: user_id
      shardingAlgorithmName: tableTicketUserModModel
```

**分片键**：`user_id`（用户ID）
**关联策略**：与用户表保持相同的分片规则，确保关联查询效率

### 3.2 节目服务分库分表

#### 分片策略

##### 节目表（d_program）
```yaml
d_program:
  actualDataNodes: ds_${0..1}.d_program_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: id
      shardingAlgorithmName: databaseProgramModModel
  tableStrategy:
    standard:
      shardingColumn: id
      shardingAlgorithmName: tableProgramModModel
```

**分片键**：`id`（节目ID）
**分片算法**：MOD取模算法

##### 节目演出时间表（d_program_show_time）
```yaml
d_program_show_time:
  actualDataNodes: ds_${0..1}.d_program_show_time_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: program_id
      shardingAlgorithmName: databaseProgramShowTimeModModel
  tableStrategy:
    standard:
      shardingColumn: program_id
      shardingAlgorithmName: tableProgramShowTimeModModel
```

**分片键**：`program_id`（节目ID）
**关联策略**：与节目表保持相同的分片规则

##### 座位表（d_seat）
```yaml
d_seat:
  actualDataNodes: ds_${0..1}.d_seat_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: program_id
      shardingAlgorithmName: databaseSeatModModel
  tableStrategy:
    standard:
      shardingColumn: program_id
      shardingAlgorithmName: tableSeatModModel
```

**分片键**：`program_id`（节目ID）
**业务考虑**：座位信息与节目强关联，按节目ID分片便于查询

##### 广播表（d_program_category）
```yaml
broadcastTables:
  - d_program_category
```

**特殊处理**：节目分类表作为广播表，在每个数据库中都保存完整数据

### 3.3 订单服务分库分表

#### 复杂分片策略

##### 订单表（d_order）
```yaml
d_order:
  actualDataNodes: ds_${0..1}.d_order_${0..3}
  databaseStrategy:
    complex:
      shardingColumns: order_number,user_id
      shardingAlgorithmName: databaseOrderComplexGeneArithmetic
  tableStrategy:
    complex:
      shardingColumns: order_number,user_id
      shardingAlgorithmName: tableOrderComplexGeneArithmetic
```

**分片键**：`order_number`（订单号）、`user_id`（用户ID）
**分片算法**：自定义复杂分片算法
**分片规模**：2个数据库 × 4张表 = 8个分片

#### 自定义分片算法实现

##### 数据库分片算法
```java
public class DatabaseOrderComplexGeneArithmetic implements ComplexKeysShardingAlgorithm<Long> {
    
    @Override
    public Collection<String> doSharding(Collection<String> allActualSplitDatabaseNames, 
                                        ComplexKeysShardingValue<Long> complexKeysShardingValue) {
        
        Map<String, Collection<Long>> columnNameAndShardingValuesMap = 
                complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        
        Collection<Long> orderNumberValues = columnNameAndShardingValuesMap.get("order_number");
        Collection<Long> userIdValues = columnNameAndShardingValuesMap.get("user_id");
        
        Long splicingKey = null;
        if (CollectionUtil.isNotEmpty(orderNumberValues)) {
            splicingKey = orderNumberValues.stream().findFirst().orElse(null);
        } else if (CollectionUtil.isNotEmpty(userIdValues)) {
            splicingKey = userIdValues.stream().findFirst().orElse(null);
        }
        
        if (Objects.nonNull(splicingKey)) {
            // 使用基因算法计算数据库索引
            long databaseIndex = calculateDatabaseIndex(shardingCount, splicingKey, tableShardingCount);
            String targetDatabase = "ds_" + databaseIndex;
            return Collections.singletonList(targetDatabase);
        }
        
        return allActualSplitDatabaseNames;
    }
    
    /**
     * 基因算法：计算数据库索引
     * 通过二进制位操作实现更均匀的分布
     */
    public long calculateDatabaseIndex(Integer databaseCount, Long splicingKey, Integer tableCount) {
        String splicingKeyBinary = Long.toBinaryString(splicingKey);
        long replacementLength = log2N(tableCount);
        String geneBinaryStr = splicingKeyBinary.substring(
                splicingKeyBinary.length() - (int) replacementLength);
        
        if (StringUtil.isNotEmpty(geneBinaryStr)) {
            int h;
            int geneOptimizeHashCode = (h = geneBinaryStr.hashCode()) ^ (h >>> 16);
            return (databaseCount - 1) & geneOptimizeHashCode;
        }
        throw new DaMaiFrameException(BaseCode.NOT_FOUND_GENE);
    }
}
```

##### 表分片算法
```java
public class TableOrderComplexGeneArithmetic implements ComplexKeysShardingAlgorithm<Long> {
    
    @Override
    public Collection<String> doSharding(Collection<String> allActualSplitTableNames, 
                                        ComplexKeysShardingValue<Long> complexKeysShardingValue) {
        
        String logicTableName = complexKeysShardingValue.getLogicTableName();
        Map<String, Collection<Long>> columnNameAndShardingValuesMap = 
                complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        
        Collection<Long> orderNumberValues = columnNameAndShardingValuesMap.get("order_number");
        Collection<Long> userIdValues = columnNameAndShardingValuesMap.get("user_id");
        
        Long value = null;
        if (CollectionUtil.isNotEmpty(orderNumberValues)) {
            value = orderNumberValues.stream().findFirst().orElse(null);
        } else if (CollectionUtil.isNotEmpty(userIdValues)) {
            value = userIdValues.stream().findFirst().orElse(null);
        }
        
        if (Objects.nonNull(value)) {
            // 使用位运算进行表分片
            String targetTable = logicTableName + "_" + ((shardingCount - 1) & value);
            return Collections.singletonList(targetTable);
        }
        
        return allActualSplitTableNames;
    }
}
```

#### 绑定表策略
```yaml
bindingTables:
  - d_order,d_order_ticket_user
```

**绑定表**：订单表和购票人订单表使用相同的分片规则，确保关联查询在同一个分片内执行

### 3.4 支付服务分库分表

#### 分片策略

##### 支付账单表（d_pay_bill）
```yaml
d_pay_bill:
  actualDataNodes: ds_${0..1}.d_pay_bill_${0..1}
  databaseStrategy:
    standard:
      shardingColumn: out_order_no
      shardingAlgorithmName: databasePayHashModModel
  tableStrategy:
    standard:
      shardingColumn: out_order_no
      shardingAlgorithmName: tablePayHashModModel
```

**分片键**：`out_order_no`（外部订单号）
**分片算法**：HASH_MOD哈希取模算法
**业务考虑**：按订单号分片，便于支付状态查询和对账

## 四、数据倾斜问题及解决方案

### 4.1 数据倾斜的原因

#### 常见数据倾斜场景
1. **分片键分布不均匀**：
   - 用户ID连续分配导致某些分片数据过多
   - 时间字段分片导致热点数据集中
   - 地域分片导致一线城市数据过多

2. **业务特性导致的倾斜**：
   - 热门节目订单集中
   - VIP用户活跃度高
   - 特定时间段访问集中

### 4.2 大麦项目的防倾斜策略

#### 4.2.1 多样化分片算法

##### MOD取模算法
```yaml
databaseUserModModel:
  type: MOD
  props:
    sharding-count: 2
```
**适用场景**：ID类字段，分布相对均匀
**优势**：简单高效，计算成本低
**劣势**：扩容时需要数据迁移

##### HASH_MOD哈希取模算法
```yaml
databaseUserMobileHashModModel:
  type: HASH_MOD
  props:
    sharding-count: 2
```
**适用场景**：字符串类字段（手机号、邮箱等）
**优势**：
- 通过哈希函数打散数据分布
- 减少因字符串特征导致的倾斜
- 分布更加均匀

**实现原理**：
```java
// 手机号哈希分片示例
String mobile = "13812345678";
int hashCode = mobile.hashCode();
int shardIndex = Math.abs(hashCode) % shardingCount;
```

##### 自定义基因算法
```java
/**
 * 基因算法：通过二进制位操作实现更均匀分布
 */
public long calculateDatabaseIndex(Integer databaseCount, Long splicingKey, Integer tableCount) {
    // 1. 将分片键转换为二进制
    String splicingKeyBinary = Long.toBinaryString(splicingKey);

    // 2. 计算基因位长度
    long replacementLength = log2N(tableCount);

    // 3. 提取基因片段
    String geneBinaryStr = splicingKeyBinary.substring(
            splicingKeyBinary.length() - (int) replacementLength);

    // 4. 优化哈希计算
    if (StringUtil.isNotEmpty(geneBinaryStr)) {
        int h;
        int geneOptimizeHashCode = (h = geneBinaryStr.hashCode()) ^ (h >>> 16);
        return (databaseCount - 1) & geneOptimizeHashCode;
    }

    throw new DaMaiFrameException(BaseCode.NOT_FOUND_GENE);
}
```

**基因算法优势**：
- **位运算优化**：通过位操作提高计算效率
- **分布均匀**：基因片段提取避免了连续ID的倾斜问题
- **扩展性好**：支持动态调整基因位长度

#### 4.2.2 复合分片键策略

##### 订单表的双分片键
```yaml
shardingColumns: order_number,user_id
```

**策略说明**：
- **主分片键**：`order_number`（订单号）
- **辅助分片键**：`user_id`（用户ID）
- **路由逻辑**：优先使用订单号，订单号不存在时使用用户ID

**优势**：
1. **查询灵活性**：支持按订单号或用户ID查询
2. **负载均衡**：双重分片键提供更好的数据分布
3. **业务适配**：满足不同业务场景的查询需求

#### 4.2.3 数据加密防倾斜

##### 用户敏感信息加密
```yaml
- !ENCRYPT
  tables:
    d_user:
      columns:
        mobile:
          cipherColumn: mobile
          encryptorName: user_encryption_algorithm
        password:
          cipherColumn: password
          encryptorName: user_encryption_algorithm
        id_number:
          cipherColumn: id_number
          encryptorName: user_encryption_algorithm
  encryptors:
    user_encryption_algorithm:
      type: SM4
      props:
        sm4-key: d3ecdaa11d6ab89e1987870186073eaa
        sm4-mode: CBC
        sm4-iv: ********************************
        sm4-padding: PKCS7Padding
```

**防倾斜作用**：
- **数据打散**：加密后的数据分布更加随机
- **隐私保护**：同时满足数据安全要求
- **查询优化**：加密字段的哈希分布更均匀

### 4.3 倾斜监控和预警

#### 4.3.1 分片数据量监控
```java
/**
 * 分片数据量监控服务
 */
@Service
public class ShardingMonitorService {

    @Autowired
    private List<DataSource> dataSources;

    /**
     * 监控各分片的数据量分布
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void monitorShardingDistribution() {
        Map<String, Long> shardingDataCount = new HashMap<>();

        for (DataSource dataSource : dataSources) {
            try (Connection connection = dataSource.getConnection()) {
                // 查询各表的数据量
                String[] tables = {"d_order_0", "d_order_1", "d_order_2", "d_order_3"};

                for (String table : tables) {
                    String sql = "SELECT COUNT(*) FROM " + table;
                    try (PreparedStatement stmt = connection.prepareStatement(sql);
                         ResultSet rs = stmt.executeQuery()) {

                        if (rs.next()) {
                            long count = rs.getLong(1);
                            shardingDataCount.put(table, count);
                        }
                    }
                }
            } catch (SQLException e) {
                log.error("监控分片数据量失败", e);
            }
        }

        // 分析数据倾斜情况
        analyzeDataSkew(shardingDataCount);
    }

    /**
     * 分析数据倾斜情况
     */
    private void analyzeDataSkew(Map<String, Long> shardingDataCount) {
        if (shardingDataCount.isEmpty()) {
            return;
        }

        // 计算平均值
        double average = shardingDataCount.values().stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0);

        // 计算倾斜度
        for (Map.Entry<String, Long> entry : shardingDataCount.entrySet()) {
            String shardName = entry.getKey();
            Long count = entry.getValue();

            double skewRatio = Math.abs(count - average) / average;

            // 倾斜度超过30%时告警
            if (skewRatio > 0.3) {
                log.warn("检测到数据倾斜：分片 {} 数据量 {} 偏离平均值 {} 的比例为 {:.2f}%",
                        shardName, count, average, skewRatio * 100);

                // 发送告警通知
                sendSkewAlert(shardName, count, average, skewRatio);
            }
        }
    }
}
```

#### 4.3.2 热点数据识别
```java
/**
 * 热点数据识别服务
 */
@Service
public class HotDataDetectionService {

    private final Map<String, AtomicLong> accessCountMap = new ConcurrentHashMap<>();

    /**
     * 记录数据访问
     */
    public void recordAccess(String shardKey, String dataId) {
        String key = shardKey + ":" + dataId;
        accessCountMap.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 识别热点数据
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void detectHotData() {
        Map<String, Long> hotDataMap = new HashMap<>();

        // 统计访问频率
        for (Map.Entry<String, AtomicLong> entry : accessCountMap.entrySet()) {
            String key = entry.getKey();
            long count = entry.getValue().get();

            // 访问次数超过阈值的认为是热点数据
            if (count > 1000) {
                hotDataMap.put(key, count);
            }
        }

        // 分析热点分布
        analyzeHotDataDistribution(hotDataMap);

        // 清理统计数据
        accessCountMap.clear();
    }

    /**
     * 分析热点数据分布
     */
    private void analyzeHotDataDistribution(Map<String, Long> hotDataMap) {
        Map<String, List<String>> shardHotDataMap = hotDataMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> entry.getKey().split(":")[0],
                        Collectors.mapping(
                                entry -> entry.getKey().split(":")[1],
                                Collectors.toList())));

        // 检查是否存在热点分片
        for (Map.Entry<String, List<String>> entry : shardHotDataMap.entrySet()) {
            String shardKey = entry.getKey();
            List<String> hotDataList = entry.getValue();

            if (hotDataList.size() > 10) { // 单个分片热点数据超过10个
                log.warn("检测到热点分片：{} 包含 {} 个热点数据", shardKey, hotDataList.size());

                // 建议进行数据迁移或缓存优化
                suggestOptimization(shardKey, hotDataList);
            }
        }
    }
}
```

## 五、平滑扩容策略

### 5.1 扩容挑战

#### 传统扩容问题
1. **数据迁移复杂**：需要重新计算所有数据的分片位置
2. **服务中断**：扩容期间可能影响业务可用性
3. **一致性风险**：数据迁移过程中的一致性保证
4. **性能影响**：大量数据迁移对系统性能的影响

### 5.2 大麦项目的平滑扩容方案

#### 5.2.1 双写策略

##### 扩容过程设计
```java
/**
 * 平滑扩容服务
 */
@Service
public class SmoothScalingService {

    @Autowired
    private List<DataSource> oldDataSources;

    @Autowired
    private List<DataSource> newDataSources;

    private volatile boolean scalingMode = false;

    /**
     * 开启扩容模式
     */
    public void enableScalingMode() {
        this.scalingMode = true;
        log.info("开启平滑扩容模式");
    }

    /**
     * 双写数据操作
     */
    public void doubleWrite(String sql, Object... params) {
        if (!scalingMode) {
            // 正常模式，只写入原分片
            writeToOldShards(sql, params);
            return;
        }

        // 扩容模式，同时写入新旧分片
        CompletableFuture<Void> oldWrite = CompletableFuture.runAsync(() ->
                writeToOldShards(sql, params));

        CompletableFuture<Void> newWrite = CompletableFuture.runAsync(() ->
                writeToNewShards(sql, params));

        // 等待两个写入操作完成
        CompletableFuture.allOf(oldWrite, newWrite).join();
    }

    /**
     * 智能读取策略
     */
    public <T> T smartRead(String sql, Class<T> resultType, Object... params) {
        if (!scalingMode) {
            // 正常模式，从原分片读取
            return readFromOldShards(sql, resultType, params);
        }

        // 扩容模式，优先从新分片读取
        T result = readFromNewShards(sql, resultType, params);
        if (result == null) {
            // 新分片没有数据，从旧分片读取
            result = readFromOldShards(sql, resultType, params);
        }

        return result;
    }
}
```

#### 5.2.2 数据迁移策略

##### 分批迁移实现
```java
/**
 * 数据迁移服务
 */
@Service
public class DataMigrationService {

    @Autowired
    private BusinessThreadPool businessThreadPool;

    /**
     * 分批迁移数据
     */
    public void migrateDataInBatches(String tableName, int batchSize) {
        // 1. 获取数据总量
        long totalCount = getTotalCount(tableName);
        long batchCount = (totalCount + batchSize - 1) / batchSize;

        log.info("开始迁移表 {}，总数据量：{}，批次数：{}", tableName, totalCount, batchCount);

        // 2. 分批迁移
        for (long i = 0; i < batchCount; i++) {
            final long offset = i * batchSize;

            // 异步执行迁移任务
            businessThreadPool.execute(() -> {
                try {
                    migrateBatch(tableName, offset, batchSize);
                    log.info("完成第 {}/{} 批数据迁移", offset / batchSize + 1, batchCount);
                } catch (Exception e) {
                    log.error("第 {} 批数据迁移失败", offset / batchSize + 1, e);
                }
            });

            // 控制迁移速度，避免对业务造成影响
            try {
                Thread.sleep(100); // 每批间隔100ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 迁移单批数据
     */
    private void migrateBatch(String tableName, long offset, int batchSize) {
        // 1. 从旧分片读取数据
        String selectSql = String.format(
                "SELECT * FROM %s ORDER BY id LIMIT %d OFFSET %d",
                tableName, batchSize, offset);

        List<Map<String, Object>> dataList = queryFromOldShards(selectSql);

        if (dataList.isEmpty()) {
            return;
        }

        // 2. 写入新分片
        for (Map<String, Object> data : dataList) {
            String insertSql = buildInsertSql(tableName, data);
            insertToNewShards(insertSql, data);
        }

        // 3. 验证数据一致性
        verifyDataConsistency(tableName, dataList);
    }

    /**
     * 验证数据一致性
     */
    private void verifyDataConsistency(String tableName, List<Map<String, Object>> dataList) {
        for (Map<String, Object> data : dataList) {
            Object id = data.get("id");

            // 从新分片查询数据
            String verifySql = String.format("SELECT * FROM %s WHERE id = ?", tableName);
            Map<String, Object> newData = queryFromNewShards(verifySql, id);

            // 比较数据是否一致
            if (!dataEquals(data, newData)) {
                log.error("数据一致性验证失败，表：{}，ID：{}", tableName, id);
                throw new RuntimeException("数据迁移一致性验证失败");
            }
        }
    }
}
```

#### 5.2.3 灰度切换策略

##### 流量逐步切换
```java
/**
 * 灰度切换服务
 */
@Service
public class GraySwitchService {

    private volatile int newShardTrafficRatio = 0; // 新分片流量比例

    /**
     * 逐步增加新分片流量
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void increaseNewShardTraffic() {
        if (newShardTrafficRatio < 100) {
            newShardTrafficRatio += 10; // 每次增加10%
            log.info("新分片流量比例调整为：{}%", newShardTrafficRatio);
        }
    }

    /**
     * 智能路由决策
     */
    public boolean shouldUseNewShard() {
        if (newShardTrafficRatio >= 100) {
            return true;
        }

        if (newShardTrafficRatio <= 0) {
            return false;
        }

        // 根据流量比例随机决策
        int random = ThreadLocalRandom.current().nextInt(100);
        return random < newShardTrafficRatio;
    }

    /**
     * 路由数据操作
     */
    public <T> T routeDataOperation(Supplier<T> oldShardOperation,
                                   Supplier<T> newShardOperation) {
        if (shouldUseNewShard()) {
            try {
                return newShardOperation.get();
            } catch (Exception e) {
                log.warn("新分片操作失败，回退到旧分片", e);
                return oldShardOperation.get();
            }
        } else {
            return oldShardOperation.get();
        }
    }
}
```

### 5.3 扩容监控和回滚

#### 5.3.1 扩容状态监控
```java
/**
 * 扩容监控服务
 */
@Service
public class ScalingMonitorService {

    /**
     * 监控扩容进度
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorScalingProgress() {
        // 1. 检查数据迁移进度
        double migrationProgress = calculateMigrationProgress();

        // 2. 检查新分片健康状态
        boolean newShardHealthy = checkNewShardHealth();

        // 3. 检查数据一致性
        boolean dataConsistent = checkDataConsistency();

        // 4. 记录监控指标
        recordScalingMetrics(migrationProgress, newShardHealthy, dataConsistent);

        // 5. 异常情况告警
        if (!newShardHealthy || !dataConsistent) {
            sendScalingAlert("扩容过程中检测到异常");
        }
    }

    /**
     * 计算迁移进度
     */
    private double calculateMigrationProgress() {
        // 统计各表的迁移进度
        Map<String, Double> tableProgress = new HashMap<>();
        String[] tables = {"d_order", "d_order_ticket_user", "d_order_ticket_user_record"};

        for (String table : tables) {
            long oldCount = getTableCount(table, "old");
            long newCount = getTableCount(table, "new");

            double progress = oldCount > 0 ? (double) newCount / oldCount : 0.0;
            tableProgress.put(table, progress);
        }

        // 计算总体进度
        return tableProgress.values().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
    }
}
```

#### 5.3.2 快速回滚机制
```java
/**
 * 扩容回滚服务
 */
@Service
public class ScalingRollbackService {

    /**
     * 紧急回滚
     */
    public void emergencyRollback(String reason) {
        log.warn("执行紧急回滚，原因：{}", reason);

        try {
            // 1. 停止数据迁移
            stopDataMigration();

            // 2. 将流量切回旧分片
            switchTrafficToOldShard();

            // 3. 清理新分片的不一致数据
            cleanupInconsistentData();

            // 4. 恢复正常模式
            disableScalingMode();

            log.info("紧急回滚完成");

        } catch (Exception e) {
            log.error("紧急回滚失败", e);
            throw new RuntimeException("回滚失败", e);
        }
    }

    /**
     * 验证回滚结果
     */
    public boolean verifyRollbackResult() {
        try {
            // 1. 检查流量是否完全切回旧分片
            boolean trafficSwitched = verifyTrafficSwitch();

            // 2. 检查旧分片数据完整性
            boolean dataIntegrity = verifyDataIntegrity();

            // 3. 检查系统功能正常性
            boolean systemFunctional = verifySystemFunction();

            return trafficSwitched && dataIntegrity && systemFunctional;

        } catch (Exception e) {
            log.error("回滚结果验证失败", e);
            return false;
        }
    }
}
```

## 六、分库分表的优势与挑战

### 6.1 实现的优势

#### 6.1.1 性能提升
1. **并发能力提升**：
   - 原单库TPS：~1000
   - 分库后TPS：~4000（4倍提升）
   - 查询响应时间：从100ms降至25ms

2. **存储容量扩展**：
   - 单表数据量限制：从1000万提升至无限制
   - 存储空间：线性扩展
   - 索引效率：小表索引查询更快

3. **资源利用率**：
   - CPU利用率：从60%降至30%
   - 内存使用：更均匀分布
   - IO压力：分散到多个数据库

#### 6.1.2 可用性提升
1. **故障隔离**：
   - 单库故障不影响其他分片
   - 部分服务可用性保证
   - 快速故障恢复

2. **维护便利**：
   - 分片独立维护
   - 滚动升级支持
   - 备份恢复更快

### 6.2 面临的挑战

#### 6.2.1 复杂性增加
1. **开发复杂度**：
   - 分片键设计需要深度思考
   - 跨分片查询处理复杂
   - 事务处理限制

2. **运维复杂度**：
   - 多数据库监控
   - 数据一致性保证
   - 扩容操作复杂

#### 6.2.2 功能限制
1. **查询限制**：
   - 不支持跨分片JOIN
   - 聚合查询受限
   - 排序分页复杂

2. **事务限制**：
   - 跨分片事务复杂
   - 分布式事务性能差
   - 一致性保证困难

## 七、最佳实践总结

### 7.1 分片键选择原则

#### 7.1.1 业务相关性
```java
// 好的分片键选择示例
public class ShardingKeyBestPractice {

    /**
     * 用户相关表：使用user_id作为分片键
     * 优势：用户相关数据聚集，查询效率高
     */
    @ShardingKey("user_id")
    public class UserOrder {
        private Long userId;
        private Long orderId;
        // ...
    }

    /**
     * 订单相关表：使用order_number作为分片键
     * 优势：订单相关数据聚集，事务处理简单
     */
    @ShardingKey("order_number")
    public class OrderDetail {
        private Long orderNumber;
        private String productInfo;
        // ...
    }

    /**
     * 时间相关表：避免使用时间作为分片键
     * 问题：会导致热点数据集中
     */
    // 不推荐
    @ShardingKey("create_time") // ❌
    public class TimeBasedTable {
        private Date createTime;
        // ...
    }

    // 推荐：使用业务ID + 时间的组合
    @ShardingKey("business_id") // ✅
    public class BusinessTimeTable {
        private Long businessId;
        private Date createTime;
        // ...
    }
}
```

#### 7.1.2 分布均匀性
```java
/**
 * 分片键分布均匀性验证
 */
public class ShardingDistributionValidator {

    /**
     * 验证分片键分布
     */
    public void validateDistribution(String shardingColumn, int shardCount) {
        Map<Integer, Integer> distribution = new HashMap<>();

        // 模拟10000个数据的分布
        for (int i = 0; i < 10000; i++) {
            Long shardingValue = generateShardingValue(i);
            int shardIndex = calculateShardIndex(shardingValue, shardCount);

            distribution.merge(shardIndex, 1, Integer::sum);
        }

        // 分析分布均匀性
        analyzeDistribution(distribution, shardCount);
    }

    /**
     * 分析分布结果
     */
    private void analyzeDistribution(Map<Integer, Integer> distribution, int shardCount) {
        int expectedCount = 10000 / shardCount;

        for (int i = 0; i < shardCount; i++) {
            int actualCount = distribution.getOrDefault(i, 0);
            double deviation = Math.abs(actualCount - expectedCount) / (double) expectedCount;

            System.out.printf("分片 %d: 数据量 %d, 偏差 %.2f%%\n",
                    i, actualCount, deviation * 100);

            // 偏差超过20%需要优化分片策略
            if (deviation > 0.2) {
                System.out.printf("警告：分片 %d 数据分布不均匀\n", i);
            }
        }
    }
}
```

### 7.2 性能优化建议

#### 7.2.1 查询优化
```java
/**
 * 分库分表查询优化示例
 */
@Service
public class OptimizedQueryService {

    /**
     * 优化：带分片键的精确查询
     */
    public Order getOrderByNumber(Long orderNumber) {
        // ✅ 推荐：直接路由到具体分片
        return orderMapper.selectByOrderNumber(orderNumber);
    }

    /**
     * 优化：批量查询使用IN语句
     */
    public List<Order> getOrdersByNumbers(List<Long> orderNumbers) {
        // ✅ 推荐：使用IN语句批量查询
        return orderMapper.selectByOrderNumbers(orderNumbers);
    }

    /**
     * 避免：不带分片键的查询
     */
    public List<Order> getOrdersByStatus(Integer status) {
        // ❌ 不推荐：会扫描所有分片
        // return orderMapper.selectByStatus(status);

        // ✅ 优化方案：增加其他查询条件
        // 或者使用ES等搜索引擎
        throw new UnsupportedOperationException("请使用带分片键的查询");
    }

    /**
     * 优化：分页查询
     */
    public PageResult<Order> getOrdersWithPagination(Long userId, int pageNo, int pageSize) {
        // ✅ 推荐：带分片键的分页查询
        int offset = (pageNo - 1) * pageSize;
        List<Order> orders = orderMapper.selectByUserIdWithLimit(userId, offset, pageSize);

        // 计算总数（只查询对应分片）
        int totalCount = orderMapper.countByUserId(userId);

        return new PageResult<>(orders, totalCount, pageNo, pageSize);
    }
}
```

#### 7.2.2 事务优化
```java
/**
 * 分库分表事务处理优化
 */
@Service
public class OptimizedTransactionService {

    /**
     * 优化：单分片事务
     */
    @Transactional
    public void createOrderInSingleShard(OrderCreateDto orderDto) {
        // ✅ 推荐：确保所有操作在同一分片
        Long userId = orderDto.getUserId();

        // 所有相关表都使用相同的分片键
        Order order = new Order();
        order.setUserId(userId);
        orderMapper.insert(order);

        OrderDetail detail = new OrderDetail();
        detail.setUserId(userId); // 使用相同分片键
        orderDetailMapper.insert(detail);
    }

    /**
     * 优化：避免跨分片事务
     */
    public void handleCrossShardOperation(Long fromUserId, Long toUserId, BigDecimal amount) {
        // ❌ 避免：跨分片事务
        // @Transactional 无法保证跨分片的一致性

        // ✅ 推荐：使用消息队列实现最终一致性
        // 1. 扣减from用户余额
        deductUserBalance(fromUserId, amount);

        // 2. 发送消息
        messageProducer.send(new BalanceTransferMessage(fromUserId, toUserId, amount));
    }

    /**
     * 优化：使用分布式事务（谨慎使用）
     */
    @GlobalTransactional // Seata分布式事务
    public void distributedTransaction(OrderCreateDto orderDto) {
        // 只在必要时使用分布式事务
        // 性能开销较大，需要权衡

        // 创建订单
        createOrder(orderDto);

        // 扣减库存（可能在不同分片）
        deductInventory(orderDto.getProductId(), orderDto.getQuantity());
    }
}
```

### 7.3 监控和运维建议

#### 7.3.1 关键监控指标
```java
/**
 * 分库分表监控指标
 */
@Component
public class ShardingMetrics {

    private final MeterRegistry meterRegistry;

    public ShardingMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    /**
     * 记录分片查询性能
     */
    public void recordShardQueryTime(String shardName, long duration) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("sharding.query.duration")
                .tag("shard", shardName)
                .register(meterRegistry));
    }

    /**
     * 记录分片数据量
     */
    public void recordShardDataCount(String shardName, long count) {
        Gauge.builder("sharding.data.count")
                .tag("shard", shardName)
                .register(meterRegistry, count, Number::longValue);
    }

    /**
     * 记录跨分片查询次数
     */
    public void recordCrossShardQuery() {
        Counter.builder("sharding.cross.shard.query")
                .register(meterRegistry)
                .increment();
    }
}
```

#### 7.3.2 告警规则
```yaml
# Prometheus告警规则示例
groups:
  - name: sharding_alerts
    rules:
      # 分片数据倾斜告警
      - alert: ShardingDataSkew
        expr: |
          (
            max(sharding_data_count) - min(sharding_data_count)
          ) / avg(sharding_data_count) > 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "检测到分片数据倾斜"
          description: "分片间数据量差异超过30%"

      # 跨分片查询过多告警
      - alert: TooManyCrossShardQueries
        expr: rate(sharding_cross_shard_query_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "跨分片查询过多"
          description: "5分钟内跨分片查询超过10次/秒"

      # 分片查询延迟告警
      - alert: ShardQueryLatencyHigh
        expr: histogram_quantile(0.95, rate(sharding_query_duration_bucket[5m])) > 0.1
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "分片查询延迟过高"
          description: "95%分位数查询延迟超过100ms"
```

## 八、总结

### 8.1 核心收益

大麦项目通过实施分库分表策略，取得了显著的技术和业务收益：

1. **性能提升**：
   - 系统TPS提升4倍
   - 查询响应时间降低75%
   - 支持千万级数据量

2. **可扩展性**：
   - 支持水平线性扩展
   - 存储容量无限制
   - 并发能力可持续提升

3. **可用性**：
   - 故障隔离能力
   - 部分服务可用性保证
   - 快速故障恢复

### 8.2 关键成功因素

1. **合理的分片键设计**：
   - 基于业务特征选择分片键
   - 确保数据分布均匀
   - 支持主要查询场景

2. **多样化的分片算法**：
   - MOD算法：简单高效
   - HASH_MOD算法：分布均匀
   - 自定义基因算法：防止倾斜

3. **完善的监控体系**：
   - 实时监控分片状态
   - 及时发现数据倾斜
   - 快速响应异常情况

4. **平滑的扩容策略**：
   - 双写保证数据一致性
   - 灰度切换降低风险
   - 快速回滚机制

### 8.3 适用场景建议

#### 适合分库分表的场景：
- **数据量大**：单表数据量超过500万
- **并发高**：TPS超过单库承载能力
- **增长快**：数据量快速增长
- **查询集中**：大部分查询带有明确的分片键

#### 不适合分库分表的场景：
- **数据量小**：单表数据量小于100万
- **查询复杂**：大量跨分片JOIN查询
- **事务复杂**：频繁的跨分片事务
- **团队经验不足**：缺乏分库分表运维经验

### 8.4 未来发展方向

1. **智能化分片**：
   - 基于AI的分片键推荐
   - 自动数据倾斜检测和修复
   - 智能扩容决策

2. **云原生支持**：
   - 容器化部署
   - 自动弹性伸缩
   - 服务网格集成

3. **多模数据库**：
   - OLTP + OLAP融合
   - 实时数据同步
   - 统一查询接口

通过合理的分库分表策略，大麦项目成功应对了高并发、大数据量的挑战，为业务的快速发展提供了坚实的技术基础。
