# 热点数据永不过期策略详解

## 项目背景

在大麦订票系统这种高并发场景下，热点数据的缓存策略至关重要。系统采用了"热点数据永不过期"的策略，通过多级缓存架构、后台定时更新和智能预热机制，确保热点数据始终可用，避免缓存雪崩和击穿问题。

## 一、热点数据永不过期策略概述

### 1.1 核心理念

**"永不过期"并非真正的永不过期**，而是通过以下机制实现：
- **逻辑过期**：数据在逻辑上过期，但物理上仍然存在
- **后台更新**：定时任务在后台异步更新过期数据
- **多级缓存**：本地缓存 + Redis缓存的双重保障
- **智能预热**：根据业务特征预先加载热点数据

### 1.2 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户请求      │───▶│   本地缓存      │───▶│   Redis缓存     │
│                 │    │  (Caffeine)     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   后台更新      │    │   数据库        │
                       │  (定时任务)     │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## 二、热点数据识别机制

### 2.1 热点数据标识

#### 数据库层面标识
```java
/**
 * 节目实体 - 热点数据标识
 */
@Data
@TableName("d_program")
public class Program extends BaseTableData implements Serializable {
    
    /**
     * 高热度节目标识
     * 0: 普通节目
     * 1: 热点节目
     */
    private Integer highHeat;
    
    // 其他字段...
}
```

#### 业务层面识别
```java
/**
 * 热点数据识别逻辑
 */
@Service
public class ProgramService {
    
    /**
     * 根据热点标识进行预热处理
     */
    public ProgramVo getDetailV2(ProgramGetDto programGetDto) {
        // 获取节目信息
        ProgramVo programVo = getByIdMultipleCache(programGetDto.getId(), showTime);
        
        // 根据热点标识进行预热
        preloadTicketUserList(programVo.getHighHeat());
        preloadAccountOrderCount(programVo.getId());
        
        return programVo;
    }
    
    /**
     * 热点数据预热逻辑
     * @param highHeat 热点标识 (0:普通 1:热点)
     */
    private void preloadTicketUserList(Integer highHeat) {
        // 只有热点节目才进行预热
        if (!Objects.equals(highHeat, 1)) {
            return;
        }
        
        // 异步预热购票人列表
        BusinessThreadPool.execute(() -> {
            try {
                // 预热逻辑实现
                loadTicketUserListToCache();
            } catch (Exception e) {
                log.error("预热加载购票人列表失败", e);
            }
        });
    }
}
```

### 2.2 热点数据分类

#### 按业务维度分类
1. **节目信息**：热门演出、明星演唱会
2. **座位信息**：热门场次的座位分布
3. **库存信息**：实时余票数量
4. **用户信息**：购票人列表、订单统计

#### 按热度级别分类
```java
/**
 * 热点数据级别枚举
 */
public enum HotDataLevel {
    /**
     * 超级热点：永不过期，实时更新
     */
    SUPER_HOT(1, "超级热点", Long.MAX_VALUE),
    
    /**
     * 热点数据：长时间缓存，定时更新
     */
    HOT(2, "热点数据", 3600L),
    
    /**
     * 温数据：中等缓存时间
     */
    WARM(3, "温数据", 1800L),
    
    /**
     * 冷数据：短时间缓存
     */
    COLD(4, "冷数据", 300L);
    
    private final Integer level;
    private final String description;
    private final Long cacheTime; // 秒
}
```

## 三、多级缓存架构实现

### 3.1 本地缓存层（Caffeine）

#### 核心配置
```java
/**
 * 节目本地缓存配置
 * 实现基于业务时间的动态过期策略
 */
@Component
public class LocalCacheProgram {
    
    private Cache<String, ProgramVo> localCache;
    
    @Value("${maximumSize:10000}")
    private Long maximumSize;
    
    @PostConstruct
    public void localLockCacheInit() {
        localCache = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfter(new Expiry<String, ProgramVo>() {
                    
                    /**
                     * 创建时过期策略
                     * 根据节目演出时间动态设置过期时间
                     */
                    @Override
                    public long expireAfterCreate(@NonNull String key, 
                                                @NonNull ProgramVo value, 
                                                long currentTime) {
                        // 计算到演出时间的秒数
                        long secondsToShow = DateUtils.countBetweenSecond(
                            DateUtils.now(), value.getShowTime());
                        
                        // 转换为纳秒（Caffeine要求）
                        return TimeUnit.MILLISECONDS.toNanos(secondsToShow * 1000);
                    }
                    
                    /**
                     * 更新时过期策略
                     * 保持原有的过期时间
                     */
                    @Override
                    public long expireAfterUpdate(@NonNull String key, 
                                                @NonNull ProgramVo value, 
                                                long currentTime, 
                                                @NonNegative long currentDuration) {
                        return currentDuration;
                    }
                    
                    /**
                     * 读取时过期策略
                     * 保持原有的过期时间（不延长）
                     */
                    @Override
                    public long expireAfterRead(@NonNull String key, 
                                              @NonNull ProgramVo value, 
                                              long currentTime, 
                                              @NonNegative long currentDuration) {
                        return currentDuration;
                    }
                })
                .build();
    }
    
    /**
     * 线程安全的缓存获取
     */
    public ProgramVo getCache(String id, Function<String, ProgramVo> function) {
        return localCache.get(id, function);
    }
    
    /**
     * 直接获取缓存（可能为null）
     */
    public ProgramVo getCache(String id) {
        return localCache.getIfPresent(id);
    }
    
    /**
     * 删除缓存
     */
    public void del(String id) {
        localCache.invalidate(id);
    }
}
```

#### 多级缓存查询策略
```java
/**
 * 多级缓存查询实现
 */
@Service
public class ProgramService {
    
    @Autowired
    private LocalCacheProgram localCacheProgram;
    
    @Autowired
    private RedisCache redisCache;
    
    /**
     * 多级缓存查询节目信息
     * 查询顺序：本地缓存 -> Redis缓存 -> 数据库
     */
    public ProgramVo getByIdMultipleCache(Long programId, Date showTime) {
        return localCacheProgram.getCache(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId).getRelKey(),
            key -> {
                log.info("本地缓存未命中，查询Redis缓存，节目ID: {}", programId);
                
                // 从Redis缓存查询
                ProgramVo programVo = getById(programId, 
                    DateUtils.countBetweenSecond(DateUtils.now(), showTime), 
                    TimeUnit.SECONDS);
                
                // 设置演出时间
                programVo.setShowTime(showTime);
                return programVo;
            });
    }
    
    /**
     * Redis缓存查询（带数据库回源）
     */
    @ServiceLock(lockType = LockType.Read, name = PROGRAM_LOCK, keys = {"#programId"})
    public ProgramVo getById(Long programId, Long expireTime, TimeUnit timeUnit) {
        // 先查Redis缓存
        ProgramVo programVo = redisCache.get(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId), 
            ProgramVo.class);
        
        if (Objects.nonNull(programVo)) {
            return programVo;
        }
        
        log.info("Redis缓存未命中，查询数据库，节目ID: {}", programId);
        
        // Redis未命中，加锁查询数据库
        RLock lock = serviceLockTool.getLock(LockType.Reentrant, GET_PROGRAM_LOCK, 
                                           new String[]{String.valueOf(programId)});
        lock.lock();
        try {
            // 双重检查，避免并发重复查询
            return redisCache.get(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId),
                ProgramVo.class,
                () -> createProgramVo(programId), // 数据库查询逻辑
                expireTime,
                timeUnit);
        } finally {
            lock.unlock();
        }
    }
}
```

### 3.2 Redis缓存层

#### 永不过期实现策略
```java
/**
 * Redis缓存实现
 * 支持永不过期和动态过期时间
 */
@Component
public class RedisCacheImpl implements RedisCache {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 带回源逻辑的缓存获取
     * 实现永不过期的核心方法
     */
    @Override
    public <T> T get(RedisKeyBuild redisKeyBuild, Class<T> clazz, 
                     Supplier<T> supplier, long ttl, TimeUnit timeUnit) {
        // 1. 先尝试从缓存获取
        T cachedValue = get(redisKeyBuild, clazz);
        
        if (CacheUtil.isEmpty(cachedValue)) {
            // 2. 缓存未命中，执行回源逻辑
            cachedValue = supplier.get();
            
            if (CacheUtil.isEmpty(cachedValue)) {
                return null;
            }
            
            // 3. 将数据写入缓存
            if (ttl == Long.MAX_VALUE) {
                // 永不过期：不设置TTL
                set(redisKeyBuild, cachedValue);
            } else {
                // 设置过期时间
                set(redisKeyBuild, cachedValue, ttl, timeUnit);
            }
        }
        
        return cachedValue;
    }
    
    /**
     * 设置缓存（永不过期版本）
     */
    @Override
    public void set(RedisKeyBuild redisKeyBuild, Object object) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();
        redisTemplate.opsForValue().set(key, JSON.toJSONString(object));
        // 注意：这里不设置过期时间，实现"永不过期"
    }
    
    /**
     * 设置缓存（带过期时间版本）
     */
    @Override
    public void set(RedisKeyBuild redisKeyBuild, Object object, long ttl, TimeUnit timeUnit) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();
        redisTemplate.opsForValue().set(key, JSON.toJSONString(object), ttl, timeUnit);
    }
}
```

## 四、后台定时更新机制

### 4.1 定时任务架构

#### 主定时任务调度器
```java
/**
 * 节目数据定时任务
 * 每天23:00执行，更新所有节目相关数据
 */
@Slf4j
@Component
public class ProgramDataTask {

    @Autowired
    private ConfigurableApplicationContext applicationContext;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramShowTimeRenewal programShowTimeRenewal;

    @Autowired
    private ProgramElasticsearchInitData programElasticsearchInitData;

    @Autowired
    private ProgramRecordTaskMapper programRecordTaskMapper;

    /**
     * 定时任务执行入口
     * Cron表达式：每天23:00执行
     */
    @Scheduled(cron = "0 0 23 * * ?")
    public void executeTask() {
        // 使用业务线程池异步执行，避免阻塞定时任务线程
        BusinessThreadPool.execute(() -> {
            try {
                log.info("节目服务定时任务开始执行");

                // 1. 获取所有节目ID列表
                List<Long> allProgramIdList = programService.getAllProgramIdList();

                if (CollectionUtil.isNotEmpty(allProgramIdList)) {
                    // 2. 逐个重置节目数据
                    for (Long programId : allProgramIdList) {
                        ProgramResetExecuteDto programResetExecuteDto = new ProgramResetExecuteDto();
                        programResetExecuteDto.setProgramId(programId);
                        programService.resetExecute(programResetExecuteDto);
                    }
                }

                // 3. 清理过期的任务记录
                programRecordTaskMapper.relDelProgramRecordTask();

                // 4. 更新节目演出时间
                programShowTimeRenewal.executeInit(applicationContext);

                // 5. 重建Elasticsearch索引
                programElasticsearchInitData.executeInit(applicationContext);

                log.info("节目服务定时任务执行完成");

            } catch (Exception e) {
                log.error("定时任务执行失败", e);
            }
        });
    }
}
```

#### 业务线程池配置
```java
/**
 * 业务线程池
 * 专门用于处理后台任务和异步操作
 */
public class BusinessThreadPool extends BaseThreadPool {

    private static ThreadPoolExecutor execute = null;

    static {
        execute = new ThreadPoolExecutor(
                // 核心线程数：CPU核心数 + 1
                Runtime.getRuntime().availableProcessors() + 1,

                // 最大线程数：CPU核心数 / 0.2（IO密集型任务）
                maximumPoolSize(),

                // 线程空闲时间：60秒
                60,
                TimeUnit.SECONDS,

                // 队列容量：600
                new ArrayBlockingQueue<>(600),

                // 线程工厂：自定义命名
                new BusinessNameThreadFactory(),

                // 拒绝策略：自定义处理
                new ThreadPoolRejectedExecutionHandler.BusinessAbortPolicy());
    }

    /**
     * 计算最大线程数
     * 适用于IO密集型任务
     */
    private static Integer maximumPoolSize() {
        return new BigDecimal(Runtime.getRuntime().availableProcessors())
                .divide(new BigDecimal("0.2"), 0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 执行异步任务
     */
    public static void execute(Runnable r) {
        execute.execute(wrapTask(r, getContextForTask(), getContextForHold()));
    }

    /**
     * 提交有返回值的任务
     */
    public static <T> Future<T> submit(Callable<T> c) {
        return execute.submit(wrapTask(c, getContextForTask(), getContextForHold()));
    }
}
```

### 4.2 数据更新策略

#### 节目演出时间更新
```java
/**
 * 节目演出时间更新服务
 * 处理即将到期的演出时间数据
 */
@Component
public class ProgramShowTimeRenewal extends AbstractApplicationPostConstructHandler {

    @Autowired
    private ProgramShowTimeService programShowTimeService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessEsHandle businessEsHandle;

    /**
     * 执行演出时间更新
     */
    @Override
    public void executeInit(final ConfigurableApplicationContext context) {
        // 1. 更新即将过期的演出时间
        Set<Long> programIdSet = programShowTimeService.renewal();

        if (!programIdSet.isEmpty()) {
            // 2. 删除Elasticsearch索引（将重建）
            businessEsHandle.deleteIndex(SpringUtil.getPrefixDistinctionName() + "-" +
                    ProgramDocumentParamName.INDEX_NAME);

            // 3. 清理相关缓存
            for (Long programId : programIdSet) {
                // 删除Redis缓存
                programService.delRedisData(programId);
                // 删除本地缓存
                programService.delLocalCache(programId);
            }
        }
    }
}
```

#### 演出时间续期逻辑
```java
/**
 * 节目演出时间服务
 */
@Service
public class ProgramShowTimeService {

    @Autowired
    private ProgramShowTimeMapper programShowTimeMapper;

    /**
     * 演出时间续期处理
     * 查找即将到期的演出（2天内），进行时间延期
     */
    @Transactional(rollbackFor = Exception.class)
    public Set<Long> renewal() {
        Set<Long> programIdSet = new HashSet<>();

        // 1. 查询2天内的演出
        LambdaQueryWrapper<ProgramShowTime> queryWrapper =
                Wrappers.lambdaQuery(ProgramShowTime.class)
                        .le(ProgramShowTime::getShowTime, DateUtils.addDay(DateUtils.now(), 2));

        List<ProgramShowTime> programShowTimes = programShowTimeMapper.selectList(queryWrapper);

        if (CollectionUtil.isEmpty(programShowTimes)) {
            return programIdSet;
        }

        // 2. 生成新的演出时间数据
        List<ProgramShowTime> newProgramShowTimes = new ArrayList<>(programShowTimes.size());

        for (ProgramShowTime programShowTime : programShowTimes) {
            programIdSet.add(programShowTime.getProgramId());

            // 创建新的演出时间（延期7天）
            ProgramShowTime newProgramShowTime = new ProgramShowTime();
            BeanUtils.copyProperties(programShowTime, newProgramShowTime);
            newProgramShowTime.setId(uidGenerator.getUid());

            // 设置新的演出时间
            Date newShowTime = DateUtils.addDay(programShowTime.getShowTime(), 7);
            newProgramShowTime.setShowTime(newShowTime);
            newProgramShowTime.setShowDayTime(DateUtils.getDateFormat(newShowTime, DateUtils.DATE_TIME_FORMAT_YYYY_MM_DD));
            newProgramShowTime.setShowWeekTime(DateUtils.getWeekOfDate(newShowTime));

            newProgramShowTimes.add(newProgramShowTime);
        }

        // 3. 批量插入新数据
        if (CollectionUtil.isNotEmpty(newProgramShowTimes)) {
            programShowTimeMapper.insertBatch(newProgramShowTimes);
        }

        // 4. 删除旧数据
        if (CollectionUtil.isNotEmpty(programShowTimes)) {
            List<Long> oldIds = programShowTimes.stream()
                    .map(ProgramShowTime::getId)
                    .collect(Collectors.toList());
            programShowTimeMapper.deleteBatchIds(oldIds);
        }

        return programIdSet;
    }
}
```

### 4.3 缓存清理和重建

#### 缓存清理策略
```java
/**
 * 节目服务 - 缓存管理
 */
@Service
public class ProgramService {

    /**
     * 删除Redis缓存数据
     */
    public void delRedisData(Long programId) {
        log.info("删除Redis缓存数据，节目ID: {}", programId);

        // 使用Lua脚本批量删除相关缓存
        programDelCacheData.delCacheData(programId);
    }

    /**
     * 删除本地缓存数据
     */
    public void delLocalCache(Long programId) {
        log.info("删除本地缓存数据，节目ID: {}", programId);

        // 删除节目基本信息缓存
        localCacheProgram.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId).getRelKey());

        // 删除节目分组缓存
        localCacheProgramGroup.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_GROUP, programId).getRelKey());

        // 删除演出时间缓存
        localCacheProgramShowTime.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SHOW_TIME, programId).getRelKey());

        // 删除票档缓存
        localCacheTicketCategory.del(programId);
    }

    /**
     * 重置节目执行状态
     * 清理所有相关缓存和临时数据
     */
    public void resetExecute(ProgramResetExecuteDto programResetExecuteDto) {
        Long programId = programResetExecuteDto.getProgramId();

        try {
            // 1. 重置数据库中的座位和库存数据
            resetDatabaseData(programId);

            // 2. 删除本地缓存
            delLocalCache(programId);

            // 3. 删除Redis缓存
            delRedisData(programId);

            log.info("节目数据重置完成，节目ID: {}", programId);

        } catch (Exception e) {
            log.error("节目数据重置失败，节目ID: {}", programId, e);
            throw new DaMaiFrameException(BaseCode.PROGRAM_RESET_FAIL);
        }
    }
}
```

## 五、智能预热机制

### 5.1 数据预热策略

#### 手动预热接口
```java
/**
 * 节目控制器 - 数据预热
 */
@RestController
@RequestMapping("/program")
public class ProgramController {

    @Autowired
    private ProgramService programService;

    /**
     * 节目数据预热接口
     * 用于压测前的数据预热
     */
    @Operation(summary = "将要压测的节目相关数据进行预热(根据id)")
    @PostMapping(value = "/data/preheat")
    public ApiResponse<Boolean> dataPreheat(@Valid @RequestBody ProgramDataPreheatDto programDataPreheatDto) {
        return ApiResponse.ok(programService.dataPreheat(programDataPreheatDto));
    }
}
```

#### 预热实现逻辑
```java
/**
 * 节目数据预热实现
 */
@Service
public class ProgramService {

    /**
     * 节目数据预热
     * 预热所有相关数据到缓存中
     */
    public Boolean dataPreheat(ProgramDataPreheatDto programDataPreheatDto) {
        Long programId = programDataPreheatDto.getProgramId();

        try {
            // 1. 先重置数据（清理旧缓存）
            ProgramResetExecuteDto programResetExecuteDto = new ProgramResetExecuteDto();
            programResetExecuteDto.setProgramId(programId);
            resetExecute(programResetExecuteDto);

            // 2. 预热节目基本信息到缓存
            ProgramGetDto programGetDto = new ProgramGetDto();
            programGetDto.setId(programId);
            ProgramVo programVo = getDetailV2(programGetDto);

            if (Objects.isNull(programVo)) {
                log.warn("节目信息不存在，预热失败，节目ID: {}", programId);
                return false;
            }

            // 3. 预热座位和库存数据
            Date showDayTime = programVo.getShowDayTime();
            List<TicketCategoryVo> ticketCategoryVoList = programVo.getTicketCategoryVoList();

            for (TicketCategoryVo ticketCategoryVo : ticketCategoryVoList) {
                // 预热座位数据
                seatService.selectSeatResolution(
                        programId,
                        ticketCategoryVo.getId(),
                        DateUtils.countBetweenSecond(DateUtils.now(), showDayTime),
                        TimeUnit.SECONDS);

                // 预热库存数据
                ticketCategoryService.getRedisRemainNumberResolution(
                        programId,
                        ticketCategoryVo.getId());
            }

            log.info("节目数据预热完成，节目ID: {}", programId);
            return true;

        } catch (Exception e) {
            log.error("节目数据预热失败，节目ID: {}", programId, e);
            return false;
        }
    }
}
```

### 5.2 智能预热触发

#### 基于热点标识的预热
```java
/**
 * 智能预热逻辑
 * 根据热点标识自动触发预热
 */
@Service
public class ProgramService {

    /**
     * 预热购票人列表（仅热点节目）
     */
    private void preloadTicketUserList(Integer highHeat) {
        // 只有热点节目才预热
        if (!Objects.equals(highHeat, 1)) {
            return;
        }

        // 获取用户信息
        String userId = BaseParameterHolder.getParameter(USER_ID);
        String code = BaseParameterHolder.getParameter(CODE);

        if (StringUtil.isEmpty(userId) || StringUtil.isEmpty(code)) {
            return;
        }

        // 验证用户登录状态
        Boolean userLogin = redisCache.hasKey(
                RedisKeyBuild.createRedisKey(RedisKeyManage.USER_LOGIN, code, userId));

        if (!userLogin) {
            return;
        }

        // 异步预热购票人列表
        BusinessThreadPool.execute(() -> {
            try {
                String ticketUserListKey = RedisKeyBuild.createRedisKey(
                        RedisKeyManage.TICKET_USER_LIST, userId).getRelKey();

                // 检查缓存是否已存在
                if (!redisCache.hasKey(ticketUserListKey)) {
                    // 调用用户服务获取购票人列表
                    TicketUserListDto ticketUserListDto = new TicketUserListDto();
                    ticketUserListDto.setUserId(Long.parseLong(userId));

                    ApiResponse<List<TicketUserVo>> apiResponse = userClient.list(ticketUserListDto);

                    if (Objects.equals(apiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                        Optional.ofNullable(apiResponse.getData())
                                .filter(CollectionUtil::isNotEmpty)
                                .ifPresent(ticketUserVoList -> {
                                    // 将购票人列表放入缓存
                                    redisCache.set(
                                            RedisKeyBuild.createRedisKey(RedisKeyManage.TICKET_USER_LIST, userId),
                                            ticketUserVoList);

                                    log.info("预热购票人列表成功，用户ID: {}, 数量: {}",
                                            userId, ticketUserVoList.size());
                                });
                    } else {
                        log.warn("获取购票人列表失败，用户ID: {}, 响应: {}",
                                userId, JSON.toJSONString(apiResponse));
                    }
                }
            } catch (Exception e) {
                log.error("预热购票人列表失败，用户ID: {}", userId, e);
            }
        });
    }

    /**
     * 预热账户订单统计（仅热点节目）
     */
    private void preloadAccountOrderCount(Long programId) {
        String userId = BaseParameterHolder.getParameter(USER_ID);
        String code = BaseParameterHolder.getParameter(CODE);

        if (StringUtil.isEmpty(userId) || StringUtil.isEmpty(code)) {
            return;
        }

        Boolean userLogin = redisCache.hasKey(
                RedisKeyBuild.createRedisKey(RedisKeyManage.USER_LOGIN, code, userId));

        if (!userLogin) {
            return;
        }

        // 异步预热订单统计
        BusinessThreadPool.execute(() -> {
            try {
                String orderCountKey = RedisKeyBuild.createRedisKey(
                        RedisKeyManage.ACCOUNT_ORDER_COUNT, userId, programId).getRelKey();

                if (!redisCache.hasKey(orderCountKey)) {
                    // 调用订单服务获取统计数据
                    AccountOrderCountDto accountOrderCountDto = new AccountOrderCountDto();
                    accountOrderCountDto.setUserId(Long.parseLong(userId));
                    accountOrderCountDto.setProgramId(programId);

                    ApiResponse<AccountOrderCountVo> apiResponse =
                            orderClient.accountOrderCount(accountOrderCountDto);

                    if (Objects.equals(apiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                        Optional.ofNullable(apiResponse.getData())
                                .ifPresent(accountOrderCountVo -> {
                                    // 将统计数据放入缓存
                                    redisCache.set(
                                            RedisKeyBuild.createRedisKey(
                                                    RedisKeyManage.ACCOUNT_ORDER_COUNT, userId, programId),
                                            accountOrderCountVo.getCount(),
                                            tokenExpireManager.getTokenExpireTime() + 1,
                                            TimeUnit.MINUTES);

                                    log.info("预热订单统计成功，用户ID: {}, 节目ID: {}, 订单数: {}",
                                            userId, programId, accountOrderCountVo.getCount());
                                });
                    } else {
                        log.warn("获取订单统计失败，用户ID: {}, 节目ID: {}, 响应: {}",
                                userId, programId, JSON.toJSONString(apiResponse));
                    }
                }
            } catch (Exception e) {
                log.error("预热订单统计失败，用户ID: {}, 节目ID: {}", userId, programId, e);
            }
        });
    }
}
```

## 六、监控和告警机制

### 6.1 缓存命中率监控

#### 缓存统计收集
```java
/**
 * 缓存监控服务
 */
@Component
public class CacheMonitorService {

    private final AtomicLong localCacheHit = new AtomicLong(0);
    private final AtomicLong localCacheMiss = new AtomicLong(0);
    private final AtomicLong redisCacheHit = new AtomicLong(0);
    private final AtomicLong redisCacheMiss = new AtomicLong(0);

    /**
     * 记录本地缓存命中
     */
    public void recordLocalCacheHit() {
        localCacheHit.incrementAndGet();
    }

    /**
     * 记录本地缓存未命中
     */
    public void recordLocalCacheMiss() {
        localCacheMiss.incrementAndGet();
    }

    /**
     * 记录Redis缓存命中
     */
    public void recordRedisCacheHit() {
        redisCacheHit.incrementAndGet();
    }

    /**
     * 记录Redis缓存未命中
     */
    public void recordRedisCacheMiss() {
        redisCacheMiss.incrementAndGet();
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        long localTotal = localCacheHit.get() + localCacheMiss.get();
        long redisTotal = redisCacheHit.get() + redisCacheMiss.get();

        return CacheStatistics.builder()
                .localCacheHitRate(localTotal > 0 ? (double) localCacheHit.get() / localTotal : 0.0)
                .redisCacheHitRate(redisTotal > 0 ? (double) redisCacheHit.get() / redisTotal : 0.0)
                .localCacheHitCount(localCacheHit.get())
                .localCacheMissCount(localCacheMiss.get())
                .redisCacheHitCount(redisCacheHit.get())
                .redisCacheMissCount(redisCacheMiss.get())
                .build();
    }
}
```

### 6.2 热点数据识别监控

#### 访问频率统计
```java
/**
 * 热点数据访问统计
 */
@Component
public class HotDataMonitor {

    private final Map<String, AtomicLong> accessCountMap = new ConcurrentHashMap<>();
    private final Map<String, Long> lastAccessTimeMap = new ConcurrentHashMap<>();

    /**
     * 记录数据访问
     */
    public void recordAccess(String dataKey) {
        // 更新访问次数
        accessCountMap.computeIfAbsent(dataKey, k -> new AtomicLong(0)).incrementAndGet();

        // 更新最后访问时间
        lastAccessTimeMap.put(dataKey, System.currentTimeMillis());
    }

    /**
     * 获取热点数据列表
     * @param threshold 热点阈值
     * @param timeWindow 时间窗口（毫秒）
     * @return 热点数据列表
     */
    public List<HotDataInfo> getHotDataList(long threshold, long timeWindow) {
        long currentTime = System.currentTimeMillis();
        List<HotDataInfo> hotDataList = new ArrayList<>();

        for (Map.Entry<String, AtomicLong> entry : accessCountMap.entrySet()) {
            String dataKey = entry.getKey();
            long accessCount = entry.getValue().get();
            Long lastAccessTime = lastAccessTimeMap.get(dataKey);

            // 检查是否在时间窗口内且访问次数超过阈值
            if (lastAccessTime != null &&
                (currentTime - lastAccessTime) <= timeWindow &&
                accessCount >= threshold) {

                hotDataList.add(HotDataInfo.builder()
                        .dataKey(dataKey)
                        .accessCount(accessCount)
                        .lastAccessTime(lastAccessTime)
                        .build());
            }
        }

        // 按访问次数降序排序
        hotDataList.sort((a, b) -> Long.compare(b.getAccessCount(), a.getAccessCount()));

        return hotDataList;
    }

    /**
     * 清理过期统计数据
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanExpiredData() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 24 * 60 * 60 * 1000; // 24小时过期

        Iterator<Map.Entry<String, Long>> iterator = lastAccessTimeMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (currentTime - entry.getValue() > expireTime) {
                String dataKey = entry.getKey();
                iterator.remove();
                accessCountMap.remove(dataKey);
            }
        }
    }
}
```

## 七、性能优化和最佳实践

### 7.1 性能优化策略

#### 批量操作优化
```java
/**
 * 批量缓存操作优化
 */
@Service
public class BatchCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 批量设置缓存
     * 使用Pipeline减少网络往返
     */
    public void batchSetCache(Map<String, Object> dataMap, long ttl, TimeUnit timeUnit) {
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String key = entry.getKey();
                String value = JSON.toJSONString(entry.getValue());

                connection.setEx(key.getBytes(), ttl, value.getBytes());
            }
            return null;
        });
    }

    /**
     * 批量获取缓存
     * 使用mget减少网络往返
     */
    public Map<String, Object> batchGetCache(List<String> keys, Class<?> valueType) {
        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        Map<String, Object> resultMap = new HashMap<>();

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = values.get(i);

            if (value != null) {
                try {
                    Object parsedValue = JSON.parseObject(value.toString(), valueType);
                    resultMap.put(key, parsedValue);
                } catch (Exception e) {
                    log.warn("解析缓存值失败，key: {}, value: {}", key, value, e);
                }
            }
        }

        return resultMap;
    }
}
```

### 7.2 最佳实践总结

#### 7.2.1 缓存设计原则

1. **分层缓存**：
   - 本地缓存：响应速度最快，容量有限
   - Redis缓存：容量大，支持分布式
   - 数据库：最终数据源

2. **过期策略**：
   - 热点数据：永不过期 + 后台更新
   - 温数据：较长过期时间
   - 冷数据：较短过期时间

3. **更新策略**：
   - 写入时：先更新数据库，再删除缓存
   - 读取时：缓存未命中时回源数据库
   - 定时任务：后台定期更新热点数据

#### 7.2.2 热点识别策略

1. **业务标识**：
   - 数据库字段标记（highHeat字段）
   - 人工运营标记热点数据

2. **访问统计**：
   - 实时统计访问频率
   - 基于时间窗口的热点识别

3. **预测算法**：
   - 基于历史数据预测热点
   - 机器学习算法识别潜在热点

#### 7.2.3 监控告警策略

1. **关键指标**：
   - 缓存命中率
   - 响应时间
   - 错误率
   - 热点数据访问量

2. **告警阈值**：
   - 缓存命中率 < 90%
   - 响应时间 > 100ms
   - 错误率 > 1%

3. **处理机制**：
   - 自动扩容
   - 降级处理
   - 人工干预

## 八、总结

### 8.1 核心优势

1. **高可用性**：
   - 多级缓存保障
   - 永不过期避免缓存雪崩
   - 后台更新保证数据新鲜度

2. **高性能**：
   - 本地缓存毫秒级响应
   - Redis缓存分布式扩展
   - 智能预热减少冷启动

3. **智能化**：
   - 自动识别热点数据
   - 动态调整缓存策略
   - 预测性数据预热

### 8.2 适用场景

1. **高并发读取**：
   - 商品详情页
   - 用户信息查询
   - 配置信息获取

2. **热点数据**：
   - 明星演唱会信息
   - 热门商品信息
   - 实时排行榜

3. **稳定性要求高**：
   - 核心业务数据
   - 基础配置信息
   - 用户会话信息

### 8.3 注意事项

1. **数据一致性**：
   - 最终一致性模型
   - 关键数据需要强一致性保障

2. **内存管理**：
   - 合理设置缓存容量
   - 定期清理过期数据

3. **监控运维**：
   - 完善的监控体系
   - 及时的告警机制
   - 快速的故障恢复

通过这套热点数据永不过期策略，大麦订票系统在高并发场景下实现了稳定、高效的数据访问，为用户提供了良好的使用体验。
```
