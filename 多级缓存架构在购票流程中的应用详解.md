# 多级缓存架构在购票流程中的应用详解

## 项目背景

大麦订票系统作为高并发的票务平台，在购票流程中面临着复杂的数据一致性挑战。系统采用了多级缓存架构（本地缓存 + Redis缓存 + 数据库），通过精心设计的缓存策略和一致性保证机制，确保在高并发场景下的数据准确性和系统性能。

## 一、多级缓存架构概述

### 1.1 缓存层级结构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户请求                                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              本地缓存 (Caffeine)                             │
│  • 响应时间：1-5ms                                          │
│  • 容量：有限 (10000条)                                     │
│  • 特点：JVM内存，最快访问                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │ 未命中
┌─────────────────────▼───────────────────────────────────────┐
│               Redis缓存                                      │
│  • 响应时间：5-20ms                                         │
│  • 容量：大 (GB级别)                                        │
│  • 特点：分布式共享，支持复杂数据结构                        │
└─────────────────────┬───────────────────────────────────────┘
                      │ 未命中
┌─────────────────────▼───────────────────────────────────────┐
│                数据库                                        │
│  • 响应时间：50-200ms                                       │
│  • 容量：无限                                               │
│  • 特点：持久化存储，ACID保证                               │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心缓存数据类型

#### 节目相关缓存
- **节目基本信息**：`PROGRAM:{programId}`
- **节目分组信息**：`PROGRAM_GROUP:{groupId}`
- **演出时间信息**：`PROGRAM_SHOW_TIME:{programId}`
- **票档列表**：`PROGRAM_TICKET_CATEGORY_LIST:{programId}`

#### 座位相关缓存
- **未售座位**：`PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH:{programId}:{ticketCategoryId}`
- **锁定座位**：`PROGRAM_SEAT_LOCK_RESOLUTION_HASH:{programId}:{ticketCategoryId}`
- **已售座位**：`PROGRAM_SEAT_SOLD_RESOLUTION_HASH:{programId}:{ticketCategoryId}`

#### 库存相关缓存
- **余票数量**：`PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION:{programId}:{ticketCategoryId}`

## 二、购票流程中的多级缓存应用

### 2.1 节目详情查询流程

#### 控制器层
```java
/**
 * 节目分类控制器
 * 展示多级缓存的查询流程
 */
@RestController
@RequestMapping("/programCategory")
public class ProgramCategoryController {
    
    @Autowired
    private ProgramCategoryService programCategoryService;
    
    /**
     * 查询节目分类列表
     * 多级缓存查询示例
     */
    @Operation(summary = "查询节目分类列表")
    @PostMapping(value = "/list")
    public ApiResponse<List<ProgramCategoryVo>> list(@Valid @RequestBody ProgramCategoryListDto programCategoryListDto) {
        return ApiResponse.ok(programCategoryService.list(programCategoryListDto));
    }
}
```

#### 服务层多级缓存实现
```java
/**
 * 节目分类服务
 * 实现多级缓存查询策略
 */
@Service
public class ProgramCategoryService {
    
    @Autowired
    private LocalCacheProgramCategory localCacheProgramCategory;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ProgramCategoryMapper programCategoryMapper;
    
    /**
     * 多级缓存查询节目分类列表
     * 查询顺序：本地缓存 -> Redis缓存 -> 数据库
     */
    public List<ProgramCategoryVo> list(ProgramCategoryListDto programCategoryListDto) {
        // 第一级：本地缓存查询
        return localCacheProgramCategory.getCache(
            "program_category_list",
            key -> {
                log.info("本地缓存未命中，查询Redis缓存");
                
                // 第二级：Redis缓存查询
                List<ProgramCategoryVo> categoryList = redisCache.getValueIsList(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_CATEGORY_LIST),
                    ProgramCategoryVo.class);
                
                if (CollectionUtil.isNotEmpty(categoryList)) {
                    return categoryList;
                }
                
                log.info("Redis缓存未命中，查询数据库");
                
                // 第三级：数据库查询
                LambdaQueryWrapper<ProgramCategory> queryWrapper = 
                    Wrappers.lambdaQuery(ProgramCategory.class)
                            .eq(ProgramCategory::getParentId, programCategoryListDto.getParentId());
                
                List<ProgramCategory> categories = programCategoryMapper.selectList(queryWrapper);
                
                List<ProgramCategoryVo> result = categories.stream()
                    .map(category -> {
                        ProgramCategoryVo vo = new ProgramCategoryVo();
                        BeanUtil.copyProperties(category, vo);
                        return vo;
                    })
                    .collect(Collectors.toList());
                
                // 将查询结果写入Redis缓存
                if (CollectionUtil.isNotEmpty(result)) {
                    redisCache.setValueIsList(
                        RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_CATEGORY_LIST),
                        result,
                        3600, // 1小时过期
                        TimeUnit.SECONDS);
                }
                
                return result;
            });
    }
}
```

### 2.2 座位信息查询流程

#### 座位服务多级缓存实现
```java
/**
 * 座位服务
 * 实现座位信息的多级缓存查询
 */
@Service
public class SeatService {
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private SeatMapper seatMapper;
    
    @Autowired
    private ProgramSeatCacheData programSeatCacheData;
    
    /**
     * 查询座位信息（带读锁保护）
     * 实现座位数据的缓存查询和数据库回源
     */
    @ServiceLock(lockType = LockType.Read, name = SEAT_LOCK, keys = {"#programId", "#ticketCategoryId"})
    public List<SeatVo> selectSeatResolution(Long programId, Long ticketCategoryId, 
                                            Long expireTime, TimeUnit timeUnit) {
        
        // 第一步：尝试从Redis缓存获取座位信息
        List<SeatVo> seatVoList = getSeatVoListByCacheResolution(programId, ticketCategoryId);
        if (CollectionUtil.isNotEmpty(seatVoList)) {
            return seatVoList;
        }
        
        // 第二步：缓存未命中，获取分布式锁进行数据库查询
        RLock lock = serviceLockTool.getLock(LockType.Reentrant, GET_SEAT_LOCK, 
                                           new String[]{String.valueOf(programId), String.valueOf(ticketCategoryId)});
        lock.lock();
        try {
            // 双重检查，避免并发重复查询
            seatVoList = getSeatVoListByCacheResolution(programId, ticketCategoryId);
            if (CollectionUtil.isNotEmpty(seatVoList)) {
                return seatVoList;
            }
            
            // 第三步：从数据库查询座位信息
            LambdaQueryWrapper<Seat> seatLambdaQueryWrapper = 
                Wrappers.lambdaQuery(Seat.class)
                        .eq(Seat::getProgramId, programId)
                        .eq(Seat::getTicketCategoryId, ticketCategoryId);
            
            List<Seat> seats = seatMapper.selectList(seatLambdaQueryWrapper);
            
            // 第四步：转换数据并按状态分组
            seatVoList = seats.stream().map(seat -> {
                SeatVo seatVo = new SeatVo();
                BeanUtil.copyProperties(seat, seatVo);
                return seatVo;
            }).collect(Collectors.toList());
            
            // 按座位状态分组
            Map<Integer, List<SeatVo>> seatMap = seatVoList.stream()
                .collect(Collectors.groupingBy(SeatVo::getSellStatus));
            
            List<SeatVo> noSoldSeatVoList = seatMap.get(SellStatus.NO_SOLD.getCode());
            List<SeatVo> lockSeatVoList = seatMap.get(SellStatus.LOCK.getCode());
            List<SeatVo> soldSeatVoList = seatMap.get(SellStatus.SOLD.getCode());
            
            // 第五步：分别缓存不同状态的座位到Redis Hash结构
            if (CollectionUtil.isNotEmpty(noSoldSeatVoList)) {
                redisCache.putHash(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH, 
                                               programId, ticketCategoryId),
                    noSoldSeatVoList.stream().collect(
                        Collectors.toMap(s -> String.valueOf(s.getId()), s -> s, (v1, v2) -> v2)),
                    expireTime, timeUnit);
            }
            
            if (CollectionUtil.isNotEmpty(lockSeatVoList)) {
                redisCache.putHash(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH, 
                                               programId, ticketCategoryId),
                    lockSeatVoList.stream().collect(
                        Collectors.toMap(s -> String.valueOf(s.getId()), s -> s, (v1, v2) -> v2)),
                    expireTime, timeUnit);
            }
            
            if (CollectionUtil.isNotEmpty(soldSeatVoList)) {
                redisCache.putHash(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_SOLD_RESOLUTION_HASH, 
                                               programId, ticketCategoryId),
                    soldSeatVoList.stream().collect(
                        Collectors.toMap(s -> String.valueOf(s.getId()), s -> s, (v1, v2) -> v2)),
                    expireTime, timeUnit);
            }
            
            // 第六步：按行列排序返回结果
            return seatVoList.stream()
                .sorted(Comparator.comparingInt(SeatVo::getRowCode)
                       .thenComparingInt(SeatVo::getColCode))
                .collect(Collectors.toList());
                
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 从Redis缓存获取座位信息
     * 使用Lua脚本聚合多个Hash的数据
     */
    public List<SeatVo> getSeatVoListByCacheResolution(Long programId, Long ticketCategoryId) {
        List<String> keys = new ArrayList<>(3);
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH,
                                            programId, ticketCategoryId).getRelKey());
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH,
                                            programId, ticketCategoryId).getRelKey());
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_SOLD_RESOLUTION_HASH,
                                            programId, ticketCategoryId).getRelKey());
        
        // 使用Lua脚本聚合查询，保证原子性
        return programSeatCacheData.getData(keys, new String[]{});
    }
}
```

### 2.3 票档余票查询流程

#### 票档服务多级缓存实现
```java
/**
 * 票档服务
 * 实现票档信息和余票数量的多级缓存
 */
@Service
public class TicketCategoryService {
    
    @Autowired
    private LocalCacheTicketCategory localCacheTicketCategory;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private TicketCategoryMapper ticketCategoryMapper;
    
    /**
     * 多级缓存查询票档列表
     * 本地缓存 -> Redis缓存 -> 数据库
     */
    public List<TicketCategoryVo> selectTicketCategoryListByProgramIdMultipleCache(Long programId, Date showTime) {
        return localCacheTicketCategory.getCache(programId, 
            key -> selectTicketCategoryListByProgramId(programId, 
                   DateUtils.countBetweenSecond(DateUtils.now(), showTime), TimeUnit.SECONDS));
    }
    
    /**
     * Redis缓存查询票档列表（带数据库回源）
     */
    @ServiceLock(lockType = LockType.Read, name = TICKET_CATEGORY_LOCK, keys = {"#programId"})
    public List<TicketCategoryVo> selectTicketCategoryListByProgramId(Long programId, 
                                                                     Long expireTime, TimeUnit timeUnit) {
        // 先从Redis缓存查询
        List<TicketCategoryVo> ticketCategoryVoList = 
            redisCache.getValueIsList(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_CATEGORY_LIST, programId), 
                TicketCategoryVo.class);
        
        if (CollectionUtil.isNotEmpty(ticketCategoryVoList)) {
            return ticketCategoryVoList;
        }
        
        // Redis未命中，加锁查询数据库
        RLock lock = serviceLockTool.getLock(LockType.Reentrant, GET_TICKET_CATEGORY_LOCK, 
                                           new String[]{String.valueOf(programId)});
        lock.lock();
        try {
            return redisCache.getValueIsList(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_CATEGORY_LIST, programId),
                TicketCategoryVo.class,
                () -> {
                    // 数据库查询逻辑
                    LambdaQueryWrapper<TicketCategory> queryWrapper = 
                        Wrappers.lambdaQuery(TicketCategory.class)
                                .eq(TicketCategory::getProgramId, programId);
                    
                    List<TicketCategory> ticketCategoryList = ticketCategoryMapper.selectList(queryWrapper);
                    
                    return ticketCategoryList.stream().map(ticketCategory -> {
                        ticketCategory.setRemainNumber(null); // 余票数量单独缓存
                        TicketCategoryVo ticketCategoryVo = new TicketCategoryVo();
                        BeanUtil.copyProperties(ticketCategory, ticketCategoryVo);
                        return ticketCategoryVo;
                    }).collect(Collectors.toList());
                }, 
                expireTime, timeUnit);
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 查询余票数量（使用Hash结构缓存）
     */
    @ServiceLock(lockType = LockType.Read, name = REMAIN_NUMBER_LOCK, keys = {"#programId", "#ticketCategoryId"})
    public Map<String, Long> getRedisRemainNumberResolution(Long programId, Long ticketCategoryId) {
        // 从Redis Hash查询余票数量
        Map<String, Long> ticketCategoryRemainNumber = 
            redisCache.getAllMapForHash(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION,
                                           programId, ticketCategoryId), 
                Long.class);
        
        if (CollectionUtil.isNotEmpty(ticketCategoryRemainNumber)) {
            return ticketCategoryRemainNumber;
        }
        
        // Hash未命中，加锁查询数据库
        RLock lock = serviceLockTool.getLock(LockType.Reentrant, GET_REMAIN_NUMBER_LOCK,
                                           new String[]{String.valueOf(programId), String.valueOf(ticketCategoryId)});
        lock.lock();
        try {
            // 双重检查
            ticketCategoryRemainNumber = 
                redisCache.getAllMapForHash(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION, 
                                               programId, ticketCategoryId), 
                    Long.class);
            
            if (CollectionUtil.isNotEmpty(ticketCategoryRemainNumber)) {
                return ticketCategoryRemainNumber;
            }
            
            // 从数据库查询
            LambdaQueryWrapper<TicketCategory> queryWrapper = 
                Wrappers.lambdaQuery(TicketCategory.class)
                        .eq(TicketCategory::getProgramId, programId)
                        .eq(TicketCategory::getId, ticketCategoryId);
            
            List<TicketCategory> ticketCategoryList = ticketCategoryMapper.selectList(queryWrapper);
            
            Map<String, Long> map = ticketCategoryList.stream()
                .collect(Collectors.toMap(
                    t -> String.valueOf(t.getId()),
                    TicketCategory::getRemainNumber, 
                    (v1, v2) -> v2));
            
            // 写入Redis Hash
            redisCache.putHash(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION,
                                           programId, ticketCategoryId),
                map);
            
            return map;
        } finally {
            lock.unlock();
        }
    }
}
```

## 三、缓存一致性问题及解决方案

### 3.1 缓存一致性挑战

#### 3.1.1 多级缓存不一致的场景

1. **时间窗口不一致**：
   - 本地缓存过期时间与Redis不同步
   - 数据更新时各级缓存更新时机不一致

2. **并发更新冲突**：
   - 多个实例同时更新缓存
   - 读写操作并发执行

3. **网络分区问题**：
   - Redis连接异常导致缓存更新失败
   - 部分节点缓存更新成功，部分失败

4. **数据库与缓存不一致**：
   - 先更新数据库，缓存更新失败
   - 先更新缓存，数据库更新失败

### 3.2 订单创建过程中的缓存一致性保证

#### 3.2.1 座位锁定的原子性操作

```java
/**
 * 订单创建过程中的座位锁定
 * 使用Lua脚本保证原子性
 */
@Service
public class ProgramOrderService {

    @Autowired
    private ProgramSeatLockCacheData programSeatLockCacheData;

    /**
     * 创建订单时的座位锁定
     * 使用Lua脚本确保原子性操作
     */
    public String create(ProgramOrderCreateDto programOrderCreateDto, Integer version) {
        // 1. 准备Lua脚本的参数
        List<String> keys = new ArrayList<>();
        Object[] data = new Object[10];

        // 构建座位数据JSON
        JSONArray jsonArray = new JSONArray();
        JSONArray addSeatDatajsonArray = new JSONArray();

        for (OrderTicketUserCreateDto orderTicketUserCreateDto :
             programOrderCreateDto.getOrderTicketUserCreateDtoList()) {

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("seatId", orderTicketUserCreateDto.getSeatId());
            jsonObject.put("ticketCategoryId", orderTicketUserCreateDto.getTicketCategoryId());
            jsonObject.put("count", 1);
            jsonArray.add(jsonObject);

            // 构建锁定座位数据
            JSONObject seatDatajsonObject = new JSONObject();
            seatDatajsonObject.put("seatId", orderTicketUserCreateDto.getSeatId());
            seatDatajsonObject.put("ticketCategoryId", orderTicketUserCreateDto.getTicketCategoryId());
            seatDatajsonObject.put("sellStatus", SellStatus.LOCK.getCode());
            seatDatajsonObject.put("lockTime", System.currentTimeMillis());
            addSeatDatajsonArray.add(seatDatajsonObject);
        }

        // 2. 构建Redis Key
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION));
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH));
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH));
        keys.add(String.valueOf(programOrderCreateDto.getProgramId()));
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_RECORD));

        Long identifierId = uidGenerator.getUid();
        keys.add(RecordType.REDUCE.getValue() + GLIDE_LINE + identifierId + GLIDE_LINE +
                programOrderCreateDto.getUserId());
        keys.add(RecordType.REDUCE.getValue());

        data[0] = JSON.toJSONString(jsonArray);
        data[1] = JSON.toJSONString(addSeatDatajsonArray);

        // 3. 执行Lua脚本，保证原子性
        String result = programSeatLockCacheData.seatLockData(keys, data);

        // 4. 检查执行结果
        if (StringUtil.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (!Objects.equals(code, BaseCode.SUCCESS.getCode())) {
                throw new DaMaiFrameException(code, jsonObject.getString("message"));
            }
        }

        // 5. 异步创建订单
        OrderCreateMq orderCreateMq = new OrderCreateMq();
        BeanUtil.copyProperties(programOrderCreateDto, orderCreateMq);
        orderCreateMq.setIdentifierId(identifierId);
        orderCreateMq.setOrderVersion(version);

        orderCreateSend.sendMessage(JSON.toJSONString(orderCreateMq));

        return String.valueOf(identifierId);
    }
}
```

#### 3.2.2 Lua脚本实现原子性操作

```lua
-- 座位锁定Lua脚本 (programSeatLock.lua)
local ticket_remain_number_hash_key = KEYS[1]
local seat_no_sold_hash_key = KEYS[2]
local seat_lock_hash_key = KEYS[3]
local program_id = KEYS[4]
local record_key = KEYS[5]
local record_value = KEYS[6]
local record_type = KEYS[7]

local seat_data_json = ARGV[1]
local add_seat_data_json = ARGV[2]

-- 解析座位数据
local seat_data_array = cjson.decode(seat_data_json)
local add_seat_data_array = cjson.decode(add_seat_data_json)

-- 验证和锁定座位的原子性操作
for i, seat_data in ipairs(seat_data_array) do
    local seat_id = tostring(seat_data.seatId)
    local ticket_category_id = tostring(seat_data.ticketCategoryId)
    local count = seat_data.count

    -- 构建具体的Redis Key
    local ticket_remain_key = ticket_remain_number_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_no_sold_key = seat_no_sold_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_lock_key = seat_lock_hash_key .. ":" .. program_id .. ":" .. ticket_category_id

    -- 1. 检查余票数量
    local remain_number = redis.call('hget', ticket_remain_key, ticket_category_id)
    if not remain_number or tonumber(remain_number) < count then
        return cjson.encode({code = 40001, message = "余票不足"})
    end

    -- 2. 检查座位状态
    local seat_vo_str = redis.call('hget', seat_no_sold_key, seat_id)
    if not seat_vo_str then
        return cjson.encode({code = 40002, message = "座位不存在或已被锁定"})
    end

    local seat_vo = cjson.decode(seat_vo_str)
    if seat_vo.sellStatus ~= 1 then -- 1表示未售卖
        return cjson.encode({code = 40003, message = "座位状态异常"})
    end

    -- 3. 原子性扣减余票
    redis.call('hincrby', ticket_remain_key, ticket_category_id, -count)

    -- 4. 原子性更新座位状态
    redis.call('hdel', seat_no_sold_key, seat_id)  -- 从未售卖中删除

    -- 构建锁定座位数据
    local lock_seat_data = add_seat_data_array[i]
    lock_seat_data.lockTime = redis.call('time')[1] -- 添加锁定时间戳
    redis.call('hset', seat_lock_key, seat_id, cjson.encode(lock_seat_data))  -- 添加到锁定中
end

-- 5. 记录操作日志
redis.call('hset', record_key, record_value, record_type)

return cjson.encode({code = 200, message = "操作成功"})
```

### 3.3 缓存更新策略

#### 3.3.1 订单状态变更时的缓存更新

```java
/**
 * 订单状态变更时的缓存一致性处理
 */
@Service
public class OrderService {

    @Autowired
    private OrderProgramCacheResolutionOperate orderProgramCacheResolutionOperate;

    /**
     * 更新订单相关的节目数据
     * 根据不同的订单版本采用不同的缓存更新策略
     */
    public void updateProgramRelatedDataResolution(Long programId, Map<Long, List<Long>> seatMap,
                                                  OrderStatus orderStatus, Long identifierId,
                                                  Long userId, List<SeatIdAndTicketUserIdDomain> seatIdAndTicketUserIdDomainList,
                                                  Integer orderVersion) {

        // 1. 从Redis缓存中查询锁定的座位信息
        Map<Long, List<SeatVo>> seatVoMap = new HashMap<>(seatMap.size());
        seatMap.forEach((ticketCategoryId, seatIdList) -> {
            seatVoMap.put(ticketCategoryId, redisCache.multiGetForHash(
                RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH,
                                           programId, ticketCategoryId),
                seatIdList.stream().map(String::valueOf).collect(Collectors.toList()),
                SeatVo.class));
        });

        if (CollectionUtil.isEmpty(seatVoMap)) {
            throw new DaMaiFrameException(BaseCode.LOCK_SEAT_LIST_EMPTY);
        }

        // 2. 构建Lua脚本参数
        List<String> keys = buildLuaScriptKeys(programId, seatVoMap, orderStatus);
        Object[] data = buildLuaScriptData(seatVoMap, orderStatus, identifierId, userId);

        ProgramOperateDataDto programOperateDataDto = new ProgramOperateDataDto();
        programOperateDataDto.setProgramId(programId);
        programOperateDataDto.setOrderVersion(orderVersion);

        // 3. 根据订单版本选择不同的处理策略
        if (!orderVersion.equals(ProgramOrderVersion.V4_VERSION.getValue())) {
            // V1-V3版本：先更新缓存，再异步更新数据库
            orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);

            if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
                programOperateDataDto.setSellStatus(SellStatus.SOLD.getCode());
                // 异步发送消息更新数据库
                delayOperateProgramDataSend.sendMessage(JSON.toJSONString(programOperateDataDto));
            }
        } else {
            // V4版本：先更新数据库，再更新缓存
            if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode()) ||
                Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {

                programOperateDataDto.setSellStatus(
                    Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())
                        ? SellStatus.SOLD.getCode() : SellStatus.NO_SOLD.getCode());

                // 先更新数据库
                ApiResponse<Boolean> programApiResponse = programClient.operateProgramData(programOperateDataDto);
                if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                    throw new DaMaiFrameException(programApiResponse);
                }
            }

            // 再更新缓存
            orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
        }
    }
}
```

#### 3.3.2 缓存失效策略

```java
/**
 * 节目缓存删除服务
 * 使用Lua脚本批量删除相关缓存，保证一致性
 */
@Component
public class ProgramDelCacheData {

    @Autowired
    private RedisCache redisCache;

    private DefaultRedisScript redisScript;

    @PostConstruct
    public void init() {
        try {
            redisScript = new DefaultRedisScript<>();
            redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("lua/programDel.lua")));
            redisScript.setResultType(Integer.class);
        } catch (Exception e) {
            log.error("redisScript init lua error", e);
        }
    }

    /**
     * 删除节目相关的所有缓存数据
     * 使用Lua脚本保证原子性删除
     */
    public void delCacheData(Long programId) {
        Program program = Optional.ofNullable(programMapper.selectById(programId))
                .orElseThrow(() -> new DaMaiFrameException(BaseCode.PROGRAM_NOT_EXIST));

        List<String> keys = new ArrayList<>();
        // 节目基本信息
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId).getRelKey());
        // 节目分组信息
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_GROUP, program.getProgramGroupId()).getRelKey());
        // 演出时间信息
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SHOW_TIME, programId).getRelKey());
        // 座位相关缓存（使用通配符）
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH, programId, "*").getRelKey());
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH, programId, "*").getRelKey());
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SEAT_SOLD_RESOLUTION_HASH, programId, "*").getRelKey());
        // 票档列表
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_CATEGORY_LIST, programId).getRelKey());
        // 余票数量（使用通配符）
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION, programId, "*").getRelKey());
        // 记录相关
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD, programId, "*").getRelKey());
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_RECORD_FINISH, programId, "*").getRelKey());
        // 丢弃订单
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, programId).getRelKey());
        // 账户订单统计（使用通配符）
        keys.add(RedisKeyBuild.createRedisKey(RedisKeyManage.ACCOUNT_ORDER_COUNT, "*", programId).getRelKey());

        // 执行Lua脚本批量删除
        redisCache.getInstance().execute(redisScript, keys, new String[]{});
    }
}
```

#### 3.3.3 Lua脚本批量删除缓存

```lua
-- 节目缓存删除Lua脚本 (programDel.lua)
local program_key = KEYS[1]
local program_group_key = KEYS[2]
local program_show_time_key = KEYS[3]
local program_seat_no_sold_resolution_hash_key = KEYS[4]
local program_seat_lock_resolution_hash_key = KEYS[5]
local program_seat_sold_resolution_hash_key = KEYS[6]
local program_ticket_category_list_key = KEYS[7]
local program_ticket_remain_number_hash_resolution_key = KEYS[8]
local program_record_hash_key = KEYS[9]
local program_record_finish_hash_key = KEYS[10]
local discard_order_key = KEYS[11]
local account_order_count_key = KEYS[12]

-- 删除基础信息缓存
redis.call('del', program_key)
redis.call('del', program_group_key)
redis.call('del', program_show_time_key)

-- 删除座位相关缓存（支持通配符）
local program_seat_no_sold_resolution_hash_list = redis.call('keys', program_seat_no_sold_resolution_hash_key)
if program_seat_no_sold_resolution_hash_list then
    for index, key in ipairs(program_seat_no_sold_resolution_hash_list) do
        redis.call('del', key)
    end
end

local program_seat_lock_resolution_hash_list = redis.call('keys', program_seat_lock_resolution_hash_key)
if program_seat_lock_resolution_hash_list then
    for index, key in ipairs(program_seat_lock_resolution_hash_list) do
        redis.call('del', key)
    end
end

local program_seat_sold_resolution_hash_list = redis.call('keys', program_seat_sold_resolution_hash_key)
if program_seat_sold_resolution_hash_list then
    for index, key in ipairs(program_seat_sold_resolution_hash_list) do
        redis.call('del', key)
    end
end

-- 删除票档列表缓存
redis.call('del', program_ticket_category_list_key)

-- 删除余票数量缓存
local program_ticket_remain_number_hash_resolution_list = redis.call('keys', program_ticket_remain_number_hash_resolution_key)
if program_ticket_remain_number_hash_resolution_list then
    for index, key in ipairs(program_ticket_remain_number_hash_resolution_list) do
        redis.call('del', key)
    end
end

-- 删除记录相关缓存
redis.call('del', program_record_hash_key)
redis.call('del', program_record_finish_hash_key)

-- 删除丢弃订单缓存
redis.call('del', discard_order_key)

-- 删除账户订单统计缓存
local account_order_count_key_list = redis.call('keys', account_order_count_key)
if account_order_count_key_list then
    for index, key in ipairs(account_order_count_key_list) do
        redis.call('del', key)
    end
end

return 1
```

### 3.4 本地缓存与Redis缓存的同步策略

#### 3.4.1 本地缓存失效机制

```java
/**
 * 本地缓存管理
 * 处理本地缓存与Redis缓存的同步
 */
@Service
public class ProgramService {

    @Autowired
    private LocalCacheProgram localCacheProgram;

    @Autowired
    private LocalCacheProgramGroup localCacheProgramGroup;

    @Autowired
    private LocalCacheProgramShowTime localCacheProgramShowTime;

    @Autowired
    private LocalCacheTicketCategory localCacheTicketCategory;

    /**
     * 删除本地缓存
     * 在数据更新时主动清理本地缓存
     */
    public void delLocalCache(Long programId) {
        log.info("删除本地缓存数据，节目ID: {}", programId);

        // 删除节目基本信息缓存
        localCacheProgram.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId).getRelKey());

        // 删除节目分组缓存
        localCacheProgramGroup.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_GROUP, programId).getRelKey());

        // 删除演出时间缓存
        localCacheProgramShowTime.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_SHOW_TIME, programId).getRelKey());

        // 删除票档缓存
        localCacheTicketCategory.del(programId);
    }

    /**
     * 重置节目执行状态
     * 清理所有相关缓存和临时数据
     */
    public void resetExecute(ProgramResetExecuteDto programResetExecuteDto) {
        Long programId = programResetExecuteDto.getProgramId();

        try {
            // 1. 重置数据库中的座位和库存数据
            resetDatabaseData(programId);

            // 2. 删除本地缓存
            delLocalCache(programId);

            // 3. 删除Redis缓存
            delRedisData(programId);

            log.info("节目数据重置完成，节目ID: {}", programId);

        } catch (Exception e) {
            log.error("节目数据重置失败，节目ID: {}", programId, e);
            throw new DaMaiFrameException(BaseCode.PROGRAM_RESET_FAIL);
        }
    }
}
```

## 四、缓存一致性保证的最佳实践

### 4.1 设计原则

#### 4.1.1 原子性保证
1. **Lua脚本**：使用Lua脚本保证多个Redis操作的原子性
2. **分布式锁**：在关键操作前获取分布式锁
3. **事务边界**：明确定义事务边界，避免跨事务的缓存操作

#### 4.1.2 一致性策略选择
```java
/**
 * 缓存一致性策略选择
 */
public enum CacheConsistencyStrategy {

    /**
     * 强一致性：先更新数据库，再删除缓存
     * 适用场景：对一致性要求极高的核心业务
     */
    STRONG_CONSISTENCY("strong", "强一致性") {
        @Override
        public void updateData(Runnable dbUpdate, Runnable cacheDelete) {
            // 1. 先更新数据库
            dbUpdate.run();
            // 2. 再删除缓存
            cacheDelete.run();
        }
    },

    /**
     * 最终一致性：先更新缓存，再异步更新数据库
     * 适用场景：对性能要求高，可以容忍短暂不一致
     */
    EVENTUAL_CONSISTENCY("eventual", "最终一致性") {
        @Override
        public void updateData(Runnable cacheUpdate, Runnable dbUpdateAsync) {
            // 1. 先更新缓存
            cacheUpdate.run();
            // 2. 异步更新数据库
            CompletableFuture.runAsync(dbUpdateAsync);
        }
    },

    /**
     * 双写一致性：同时更新数据库和缓存
     * 适用场景：平衡性能和一致性
     */
    DUAL_WRITE_CONSISTENCY("dual", "双写一致性") {
        @Override
        public void updateData(Runnable dbUpdate, Runnable cacheUpdate) {
            // 使用CompletableFuture并行执行
            CompletableFuture<Void> dbFuture = CompletableFuture.runAsync(dbUpdate);
            CompletableFuture<Void> cacheFuture = CompletableFuture.runAsync(cacheUpdate);

            // 等待两个操作都完成
            CompletableFuture.allOf(dbFuture, cacheFuture).join();
        }
    };

    private final String code;
    private final String description;

    CacheConsistencyStrategy(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public abstract void updateData(Runnable... operations);
}
```

### 4.2 监控和告警

#### 4.2.1 缓存一致性监控
```java
/**
 * 缓存一致性监控服务
 */
@Service
public class CacheConsistencyMonitor {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ProgramMapper programMapper;

    /**
     * 检查缓存与数据库的一致性
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void checkCacheConsistency() {
        // 随机抽样检查
        List<Long> sampleProgramIds = getSampleProgramIds(100);

        for (Long programId : sampleProgramIds) {
            try {
                checkProgramDataConsistency(programId);
            } catch (Exception e) {
                log.error("检查节目数据一致性失败，节目ID: {}", programId, e);
            }
        }
    }

    /**
     * 检查单个节目的数据一致性
     */
    private void checkProgramDataConsistency(Long programId) {
        // 1. 从数据库查询
        Program dbProgram = programMapper.selectById(programId);
        if (dbProgram == null) {
            return;
        }

        // 2. 从Redis缓存查询
        ProgramVo cacheProgram = redisCache.get(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId),
            ProgramVo.class);

        // 3. 比较数据一致性
        if (cacheProgram != null) {
            boolean isConsistent = compareProgram(dbProgram, cacheProgram);
            if (!isConsistent) {
                log.warn("检测到缓存数据不一致，节目ID: {}", programId);

                // 发送告警
                sendInconsistencyAlert(programId, dbProgram, cacheProgram);

                // 自动修复：删除不一致的缓存
                redisCache.del(RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId));
            }
        }
    }

    /**
     * 比较数据库和缓存中的节目数据
     */
    private boolean compareProgram(Program dbProgram, ProgramVo cacheProgram) {
        return Objects.equals(dbProgram.getId(), cacheProgram.getId()) &&
               Objects.equals(dbProgram.getTitle(), cacheProgram.getTitle()) &&
               Objects.equals(dbProgram.getStatus(), cacheProgram.getStatus()) &&
               Objects.equals(dbProgram.getUpdateTime(), cacheProgram.getUpdateTime());
    }

    /**
     * 发送数据不一致告警
     */
    private void sendInconsistencyAlert(Long programId, Program dbProgram, ProgramVo cacheProgram) {
        String alertMessage = String.format(
            "缓存数据不一致告警：节目ID=%d, 数据库更新时间=%s, 缓存更新时间=%s",
            programId,
            dbProgram.getUpdateTime(),
            cacheProgram.getUpdateTime());

        // 发送到监控系统
        monitoringService.sendAlert("CACHE_INCONSISTENCY", alertMessage);
    }
}
```

#### 4.2.2 缓存性能监控
```java
/**
 * 缓存性能监控
 */
@Component
public class CachePerformanceMonitor {

    private final AtomicLong localCacheHit = new AtomicLong(0);
    private final AtomicLong localCacheMiss = new AtomicLong(0);
    private final AtomicLong redisCacheHit = new AtomicLong(0);
    private final AtomicLong redisCacheMiss = new AtomicLong(0);
    private final AtomicLong dbQuery = new AtomicLong(0);

    /**
     * 记录缓存访问统计
     */
    public void recordCacheAccess(CacheLevel level, boolean hit) {
        switch (level) {
            case LOCAL:
                if (hit) {
                    localCacheHit.incrementAndGet();
                } else {
                    localCacheMiss.incrementAndGet();
                }
                break;
            case REDIS:
                if (hit) {
                    redisCacheHit.incrementAndGet();
                } else {
                    redisCacheMiss.incrementAndGet();
                }
                break;
            case DATABASE:
                dbQuery.incrementAndGet();
                break;
        }
    }

    /**
     * 获取缓存统计信息
     */
    @Scheduled(fixedRate = 60000) // 每分钟输出一次统计
    public void reportCacheStatistics() {
        long localTotal = localCacheHit.get() + localCacheMiss.get();
        long redisTotal = redisCacheHit.get() + redisCacheMiss.get();

        double localHitRate = localTotal > 0 ? (double) localCacheHit.get() / localTotal : 0.0;
        double redisHitRate = redisTotal > 0 ? (double) redisCacheHit.get() / redisTotal : 0.0;

        log.info("缓存统计 - 本地缓存命中率: {:.2f}%, Redis缓存命中率: {:.2f}%, 数据库查询次数: {}",
                localHitRate * 100, redisHitRate * 100, dbQuery.get());

        // 重置计数器
        localCacheHit.set(0);
        localCacheMiss.set(0);
        redisCacheHit.set(0);
        redisCacheMiss.set(0);
        dbQuery.set(0);
    }

    public enum CacheLevel {
        LOCAL, REDIS, DATABASE
    }
}
```

### 4.3 故障恢复机制

#### 4.3.1 缓存降级策略
```java
/**
 * 缓存降级服务
 */
@Service
public class CacheDegradationService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ProgramMapper programMapper;

    private volatile boolean redisAvailable = true;

    /**
     * 带降级的缓存查询
     */
    public ProgramVo getProgramWithDegradation(Long programId) {
        try {
            // 尝试从Redis查询
            if (redisAvailable) {
                ProgramVo programVo = redisCache.get(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM, programId),
                    ProgramVo.class);

                if (programVo != null) {
                    return programVo;
                }
            }
        } catch (Exception e) {
            log.warn("Redis查询失败，启用降级策略，节目ID: {}", programId, e);
            redisAvailable = false;

            // 启动Redis恢复检查
            scheduleRedisRecoveryCheck();
        }

        // 降级到数据库查询
        return queryFromDatabase(programId);
    }

    /**
     * 从数据库查询
     */
    private ProgramVo queryFromDatabase(Long programId) {
        Program program = programMapper.selectById(programId);
        if (program == null) {
            return null;
        }

        ProgramVo programVo = new ProgramVo();
        BeanUtil.copyProperties(program, programVo);
        return programVo;
    }

    /**
     * 定期检查Redis恢复状态
     */
    private void scheduleRedisRecoveryCheck() {
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(30000); // 等待30秒

                // 尝试执行简单的Redis操作
                redisCache.set(RedisKeyBuild.createRedisKey(RedisKeyManage.HEALTH_CHECK),
                              "test", 10, TimeUnit.SECONDS);

                redisAvailable = true;
                log.info("Redis服务已恢复");

            } catch (Exception e) {
                log.warn("Redis服务仍未恢复，继续使用降级策略");
                // 递归调用，继续检查
                scheduleRedisRecoveryCheck();
            }
        });
    }
}
```

## 五、总结

### 5.1 多级缓存架构的核心价值

#### 5.1.1 性能提升
- **响应时间优化**：本地缓存1-5ms，Redis缓存5-20ms，相比数据库50-200ms大幅提升
- **并发能力增强**：多级缓存分散访问压力，提高系统整体并发处理能力
- **资源利用率**：合理利用内存和网络资源，降低数据库压力

#### 5.1.2 可用性保障
- **故障隔离**：单级缓存故障不影响整体服务可用性
- **降级机制**：自动降级到下一级缓存或数据库
- **快速恢复**：缓存预热和自动恢复机制

#### 5.1.3 数据一致性
- **原子性操作**：使用Lua脚本保证多个操作的原子性
- **分布式锁**：关键操作使用分布式锁保证并发安全
- **监控告警**：实时监控缓存一致性，及时发现和处理问题

### 5.2 关键技术要点

#### 5.2.1 缓存设计原则
1. **分层设计**：本地缓存 + Redis缓存 + 数据库的三层架构
2. **数据分类**：根据访问频率和重要性对数据进行分类缓存
3. **过期策略**：合理设置缓存过期时间，平衡性能和一致性

#### 5.2.2 一致性保证机制
1. **Lua脚本**：保证Redis多个操作的原子性
2. **分布式锁**：避免并发操作导致的数据不一致
3. **版本控制**：通过版本号控制不同的缓存更新策略

#### 5.2.3 监控和运维
1. **性能监控**：实时监控各级缓存的命中率和响应时间
2. **一致性检查**：定期检查缓存与数据库的数据一致性
3. **故障恢复**：自动降级和恢复机制

### 5.3 适用场景和建议

#### 5.3.1 适用场景
- **高并发读取**：大量用户同时查询节目信息、座位状态
- **热点数据**：明星演唱会、热门演出等高频访问数据
- **复杂查询**：需要多表关联的复杂查询结果缓存

#### 5.3.2 实施建议
1. **渐进式实施**：从核心业务开始，逐步扩展到其他业务
2. **监控先行**：在实施缓存前建立完善的监控体系
3. **容量规划**：合理规划各级缓存的容量和过期策略
4. **团队培训**：确保团队掌握缓存设计和运维技能

通过精心设计的多级缓存架构和完善的一致性保证机制，大麦订票系统在高并发购票场景下实现了优异的性能表现和可靠的数据一致性，为用户提供了流畅的购票体验。
```
```
