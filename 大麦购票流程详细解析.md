# 大麦购票流程详细解析

## 项目背景

通过仔细阅读大麦项目代码，我将详细解析整个购票流程的实现机制，包括幂等性检验、分布式锁、组合模式业务验证、选座计算、缓存更新、订单创建、延迟队列处理等核心环节。

## 一、购票流程总览

### 1.1 流程架构图

```
用户购票请求
    ↓
【1. 幂等性检验】@RepeatExecuteLimit
    ↓
【2. 分布式锁】@ServiceLock
    ↓
【3. 组合模式业务验证】CompositeContainer
    ↓
【4. 多级缓存查询】本地缓存→Redis→数据库
    ↓
【5. 选座算法】SeatMatch.findAdjacentSeatVos
    ↓
【6. 价格计算与验证】
    ↓
【7. Redis缓存更新】Lua脚本原子操作
    ↓
【8. 调用订单服务】OrderClient.create
    ↓
【9. 订单创建】数据库事务
    ↓
【10. 延迟队列】DelayOrderCancelSend
    ↓
【11. 成功/失败处理】缓存回滚/释放锁
```

### 1.2 核心组件

- **节目服务**：`ProgramOrderService` - 购票流程控制
- **订单服务**：`OrderService` - 订单创建和管理
- **缓存服务**：`RedisCache` - 多级缓存管理
- **锁服务**：`ServiceLockTool` - 分布式锁管理
- **延迟队列**：`DelayQueueContext` - 订单超时处理

## 二、详细流程解析

### 2.1 购票请求入口

#### 控制器层
```java
/**
 * 节目订单控制器
 * 处理购票请求的入口
 */
@RestController
@RequestMapping("/program/order")
public class ProgramOrderController {
    
    @Autowired
    private ProgramOrderService programOrderService;
    
    /**
     * 创建订单接口
     * 支持多个版本的购票流程
     */
    @Operation(summary = "创建订单")
    @PostMapping(value = "/create")
    public ApiResponse<String> create(@Valid @RequestBody ProgramOrderCreateDto programOrderCreateDto) {
        // 调用不同版本的购票流程
        return ApiResponse.ok(programOrderService.create(programOrderCreateDto, 
                                                       ProgramOrderVersion.V1_VERSION.getValue()));
    }
}
```

#### 请求参数结构
```java
/**
 * 购票请求参数
 */
@Data
public class ProgramOrderCreateDto {
    @NotNull
    private Long programId;        // 节目ID
    
    @NotNull
    private Long userId;           // 用户ID
    
    @NotNull
    private List<Long> ticketUserIdList;  // 购票人ID列表
    
    private List<SeatDto> seatDtoList;    // 指定座位列表（选座模式）
    
    private Long ticketCategoryId;        // 票档ID（非选座模式）
    
    private Integer ticketCount;          // 票数（非选座模式）
}
```

### 2.2 幂等性检验

#### @RepeatExecuteLimit注解实现
```java
/**
 * 防重复执行注解
 * 防止用户短时间内重复提交购票请求
 */
@RepeatExecuteLimit(
    name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
    keys = {"#programOrderCreateDto.userId", "#programOrderCreateDto.programId"},
    durationTime = 60,  // 60秒内防重复
    message = "购票请求过于频繁，请稍后重试"
)
public String create(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
    // 购票逻辑
}
```

#### 幂等性检验流程
```java
/**
 * RepeatExecuteLimitAspect切面实现
 */
@Around("@annotation(repeatLimit)")
public Object around(ProceedingJoinPoint joinPoint, RepeatExecuteLimit repeatLimit) throws Throwable {
    // 1. 构建幂等性检验Key
    String lockName = lockInfoHandle.getLockName(joinPoint, repeatLimit.name(), repeatLimit.keys());
    String repeatFlagName = PREFIX_NAME + lockName;
    
    // 2. 检查是否已经执行过
    String flagObject = redissonDataHandle.get(repeatFlagName);
    if (SUCCESS_FLAG.equals(flagObject)) {
        throw new DaMaiFrameException("操作过于频繁，请稍后重试");
    }
    
    // 3. 获取本地锁（第一层保护）
    ReentrantLock localLock = localLockMap.computeIfAbsent(lockName, k -> new ReentrantLock());
    localLock.lock();
    
    try {
        // 4. 获取分布式锁（第二层保护）
        ServiceLocker lock = serviceLockFactory.getLock(LockType.Fair);
        boolean result = lock.tryLock(lockName, TimeUnit.SECONDS, 0);
        
        if (result) {
            try {
                // 5. 双重检查
                flagObject = redissonDataHandle.get(repeatFlagName);
                if (SUCCESS_FLAG.equals(flagObject)) {
                    throw new DaMaiFrameException("操作过于频繁，请稍后重试");
                }
                
                // 6. 执行业务逻辑
                Object obj = joinPoint.proceed();
                
                // 7. 设置执行标记
                if (repeatLimit.durationTime() > 0) {
                    redissonDataHandle.set(repeatFlagName, SUCCESS_FLAG, 
                                         repeatLimit.durationTime(), TimeUnit.SECONDS);
                }
                
                return obj;
            } finally {
                lock.unlock(lockName);
            }
        } else {
            throw new DaMaiFrameException("操作过于频繁，请稍后重试");
        }
    } finally {
        localLock.unlock();
    }
}
```

### 2.3 分布式锁保护

#### @ServiceLock注解实现
```java
/**
 * 分布式锁注解
 * 保证同一节目的购票操作串行执行
 */
@ServiceLock(
    name = PROGRAM_ORDER_CREATE_V1, 
    keys = {"#programOrderCreateDto.programId"},
    lockType = LockType.Reentrant,
    waitTime = 10,
    timeUnit = TimeUnit.SECONDS
)
public String create(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
    // 购票逻辑
}
```

#### 分布式锁实现机制
```java
/**
 * ServiceLockAspect切面实现
 */
@Around("@annotation(servicelock)")
public Object around(ProceedingJoinPoint joinPoint, ServiceLock servicelock) throws Throwable {
    // 1. 构建锁名称
    String lockName = lockInfoHandle.getLockName(joinPoint, servicelock.name(), servicelock.keys());
    
    // 2. 获取指定类型的锁
    LockType lockType = servicelock.lockType();
    ServiceLocker lock = serviceLockFactory.getLock(lockType);
    
    // 3. 尝试获取锁
    boolean result = lock.tryLock(lockName, servicelock.timeUnit(), servicelock.waitTime());
    
    if (result) {
        try {
            // 4. 执行业务逻辑
            return joinPoint.proceed();
        } finally {
            // 5. 释放锁
            lock.unlock(lockName);
        }
    } else {
        // 6. 获取锁失败，执行超时策略
        servicelock.lockTimeoutStrategy().handler(lockName);
        return null;
    }
}
```

### 2.4 组合模式业务验证

#### 验证组件树结构
```java
/**
 * 抽象组合组件
 * 构建树形验证结构
 */
public abstract class AbstractComposite<T> {
    protected List<AbstractComposite<T>> list = new ArrayList<>();
    
    protected abstract void execute(T param);
    public abstract String type();
    public abstract Integer executeParentOrder();
    public abstract Integer executeTier();
    public abstract Integer executeOrder();
    
    /**
     * 按层次结构执行验证
     * 使用广度优先遍历
     */
    public void allExecute(T param) {
        Queue<AbstractComposite<T>> queue = new LinkedList<>();
        queue.add(this);
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();
            
            // 处理当前层级的所有节点
            for (int i = 0; i < levelSize; i++) {
                AbstractComposite<T> current = queue.poll();
                current.execute(param);  // 执行验证逻辑
                queue.addAll(current.list);  // 添加子节点
            }
        }
    }
}
```

#### 购票验证组件实现
```java
/**
 * 购票基础验证组件
 */
@Component
public class ProgramOrderCreateCheck extends AbstractComposite<ProgramOrderCreateDto> {
    
    @Override
    protected void execute(ProgramOrderCreateDto param) {
        // 1. 验证节目是否存在
        if (param.getProgramId() == null) {
            throw new DaMaiFrameException(BaseCode.PROGRAM_ID_EMPTY);
        }
        
        // 2. 验证用户是否存在
        if (param.getUserId() == null) {
            throw new DaMaiFrameException(BaseCode.USER_ID_EMPTY);
        }
        
        // 3. 验证购票人列表
        if (CollectionUtil.isEmpty(param.getTicketUserIdList())) {
            throw new DaMaiFrameException(BaseCode.TICKET_USER_LIST_EMPTY);
        }
        
        // 4. 验证购票数量限制
        if (param.getTicketUserIdList().size() > MAX_TICKET_COUNT) {
            throw new DaMaiFrameException(BaseCode.TICKET_COUNT_EXCEED_LIMIT);
        }
    }
    
    @Override
    public String type() {
        return CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue();
    }
    
    @Override
    public Integer executeParentOrder() {
        return 0; // 根节点
    }
    
    @Override
    public Integer executeTier() {
        return 1; // 第一层
    }
    
    @Override
    public Integer executeOrder() {
        return 1; // 执行顺序
    }
}
```

#### 验证执行入口
```java
/**
 * 组合容器管理器
 */
@Component
public class CompositeContainer<T> {
    
    private final Map<String, AbstractComposite> allCompositeInterfaceMap = new HashMap<>();
    
    /**
     * 执行指定类型的组合验证
     */
    public void execute(String type, T param) {
        AbstractComposite compositeInterface = Optional.ofNullable(allCompositeInterfaceMap.get(type))
                .orElseThrow(() -> new DaMaiFrameException(BaseCode.COMPOSITE_NOT_EXIST));
        
        // 执行整个验证树
        compositeInterface.allExecute(param);
    }
}

// 在购票服务中使用
public String create(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
    // 执行组合验证链
    compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue(), 
                              programOrderCreateDto);
    
    // 继续购票流程...
}
```

### 2.5 多级缓存查询和选座算法

#### 座位信息查询
```java
/**
 * 座位服务
 * 实现多级缓存查询和选座算法
 */
@Service
public class SeatService {

    /**
     * 查询可用座位（带读锁保护）
     */
    @ServiceLock(lockType = LockType.Read, name = SEAT_LOCK, keys = {"#programId", "#ticketCategoryId"})
    public List<SeatVo> selectSeatResolution(Long programId, Long ticketCategoryId,
                                            Long expireTime, TimeUnit timeUnit) {

        // 第一步：尝试从Redis缓存获取座位信息
        List<SeatVo> seatVoList = getSeatVoListByCacheResolution(programId, ticketCategoryId);
        if (CollectionUtil.isNotEmpty(seatVoList)) {
            return seatVoList;
        }

        // 第二步：缓存未命中，获取分布式锁进行数据库查询
        RLock lock = serviceLockTool.getLock(LockType.Reentrant, GET_SEAT_LOCK,
                                           new String[]{String.valueOf(programId), String.valueOf(ticketCategoryId)});
        lock.lock();
        try {
            // 双重检查，避免并发重复查询
            seatVoList = getSeatVoListByCacheResolution(programId, ticketCategoryId);
            if (CollectionUtil.isNotEmpty(seatVoList)) {
                return seatVoList;
            }

            // 第三步：从数据库查询座位信息
            List<Seat> seats = seatMapper.selectList(
                Wrappers.lambdaQuery(Seat.class)
                        .eq(Seat::getProgramId, programId)
                        .eq(Seat::getTicketCategoryId, ticketCategoryId));

            // 第四步：转换数据并按状态分组缓存
            seatVoList = seats.stream().map(seat -> {
                SeatVo seatVo = new SeatVo();
                BeanUtil.copyProperties(seat, seatVo);
                return seatVo;
            }).collect(Collectors.toList());

            // 按座位状态分组缓存到Redis Hash
            Map<Integer, List<SeatVo>> seatMap = seatVoList.stream()
                .collect(Collectors.groupingBy(SeatVo::getSellStatus));

            // 分别缓存不同状态的座位
            cacheSeatsGroupByStatus(programId, ticketCategoryId, seatMap, expireTime, timeUnit);

            return seatVoList.stream()
                .sorted(Comparator.comparingInt(SeatVo::getRowCode)
                       .thenComparingInt(SeatVo::getColCode))
                .collect(Collectors.toList());

        } finally {
            lock.unlock();
        }
    }

    /**
     * 智能选座算法
     * 优先选择相邻座位
     */
    public List<SeatVo> findAdjacentSeats(List<SeatVo> availableSeats, int ticketCount) {
        if (ticketCount == 1) {
            // 单张票，随机选择
            return Collections.singletonList(availableSeats.get(0));
        }

        // 按行分组
        Map<Integer, List<SeatVo>> seatsByRow = availableSeats.stream()
            .collect(Collectors.groupingBy(SeatVo::getRowCode));

        // 优先在同一行寻找连续座位
        for (Map.Entry<Integer, List<SeatVo>> entry : seatsByRow.entrySet()) {
            List<SeatVo> rowSeats = entry.getValue();
            rowSeats.sort(Comparator.comparingInt(SeatVo::getColCode));

            // 寻找连续座位
            List<SeatVo> consecutiveSeats = findConsecutiveSeats(rowSeats, ticketCount);
            if (consecutiveSeats.size() == ticketCount) {
                return consecutiveSeats;
            }
        }

        // 如果找不到连续座位，选择最接近的座位
        return selectNearestSeats(availableSeats, ticketCount);
    }

    /**
     * 寻找连续座位
     */
    private List<SeatVo> findConsecutiveSeats(List<SeatVo> rowSeats, int ticketCount) {
        List<SeatVo> result = new ArrayList<>();

        for (int i = 0; i <= rowSeats.size() - ticketCount; i++) {
            boolean isConsecutive = true;

            // 检查是否连续
            for (int j = 1; j < ticketCount; j++) {
                if (rowSeats.get(i + j).getColCode() != rowSeats.get(i + j - 1).getColCode() + 1) {
                    isConsecutive = false;
                    break;
                }
            }

            if (isConsecutive) {
                for (int j = 0; j < ticketCount; j++) {
                    result.add(rowSeats.get(i + j));
                }
                break;
            }
        }

        return result;
    }
}
```

### 2.6 价格计算与验证

#### 票价计算服务
```java
/**
 * 票价计算服务
 */
@Service
public class TicketPriceCalculator {

    /**
     * 计算订单总价
     */
    public BigDecimal calculateTotalPrice(List<SeatVo> selectedSeats,
                                        Map<Long, TicketCategoryVo> ticketCategoryMap) {
        BigDecimal totalPrice = BigDecimal.ZERO;

        for (SeatVo seat : selectedSeats) {
            TicketCategoryVo ticketCategory = ticketCategoryMap.get(seat.getTicketCategoryId());
            if (ticketCategory == null) {
                throw new DaMaiFrameException(BaseCode.TICKET_CATEGORY_NOT_EXIST);
            }

            // 累加票价
            totalPrice = totalPrice.add(ticketCategory.getPrice());
        }

        return totalPrice;
    }

    /**
     * 验证价格一致性
     */
    public void validatePrice(BigDecimal calculatedPrice, BigDecimal requestPrice) {
        if (calculatedPrice.compareTo(requestPrice) != 0) {
            throw new DaMaiFrameException(BaseCode.PRICE_INCONSISTENT);
        }
    }
}
```

### 2.7 Redis缓存更新（Lua脚本原子操作）

#### 座位锁定的原子操作
```java
/**
 * 购票核心逻辑
 * 使用Lua脚本保证原子性
 */
public String create(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
    // 前置验证已完成...

    // 1. 准备Lua脚本参数
    List<String> keys = new ArrayList<>();
    Object[] data = new Object[10];

    // 构建座位数据JSON
    JSONArray seatDataArray = new JSONArray();
    JSONArray lockSeatDataArray = new JSONArray();

    for (OrderTicketUserCreateDto orderTicketUserCreateDto :
         programOrderCreateDto.getOrderTicketUserCreateDtoList()) {

        // 扣减库存数据
        JSONObject seatData = new JSONObject();
        seatData.put("seatId", orderTicketUserCreateDto.getSeatId());
        seatData.put("ticketCategoryId", orderTicketUserCreateDto.getTicketCategoryId());
        seatData.put("count", 1);
        seatDataArray.add(seatData);

        // 锁定座位数据
        JSONObject lockSeatData = new JSONObject();
        lockSeatData.put("seatId", orderTicketUserCreateDto.getSeatId());
        lockSeatData.put("ticketCategoryId", orderTicketUserCreateDto.getTicketCategoryId());
        lockSeatData.put("sellStatus", SellStatus.LOCK.getCode());
        lockSeatData.put("lockTime", System.currentTimeMillis());
        lockSeatData.put("userId", programOrderCreateDto.getUserId());
        lockSeatDataArray.add(lockSeatData);
    }

    // 2. 构建Redis Key列表
    keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION));
    keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH));
    keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH));
    keys.add(String.valueOf(programOrderCreateDto.getProgramId()));
    keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_RECORD));

    Long identifierId = uidGenerator.getUid();
    keys.add(RecordType.REDUCE.getValue() + GLIDE_LINE + identifierId + GLIDE_LINE +
            programOrderCreateDto.getUserId());
    keys.add(RecordType.REDUCE.getValue());

    data[0] = JSON.toJSONString(seatDataArray);
    data[1] = JSON.toJSONString(lockSeatDataArray);

    // 3. 执行Lua脚本，保证原子性
    String result = programSeatLockCacheData.seatLockData(keys, data);

    // 4. 检查执行结果
    if (StringUtil.isNotEmpty(result)) {
        JSONObject jsonObject = JSON.parseObject(result);
        Integer code = jsonObject.getInteger("code");
        if (!Objects.equals(code, BaseCode.SUCCESS.getCode())) {
            throw new DaMaiFrameException(code, jsonObject.getString("message"));
        }
    }

    // 5. 调用订单服务创建订单
    OrderCreateDto orderCreateDto = buildCreateOrderParam(programOrderCreateDto, selectedSeats, orderVersion);
    String orderNumber = createOrderByRpc(orderCreateDto, selectedSeats);

    // 6. 发送延迟取消消息
    DelayOrderCancelDto delayOrderCancelDto = new DelayOrderCancelDto();
    delayOrderCancelDto.setOrderNumber(Long.valueOf(orderNumber));
    delayOrderCancelSend.sendMessage(JSON.toJSONString(delayOrderCancelDto));

    return String.valueOf(identifierId);
}
```

#### Lua脚本实现
```lua
-- 座位锁定Lua脚本 (programSeatLock.lua)
local ticket_remain_number_hash_key = KEYS[1]
local seat_no_sold_hash_key = KEYS[2]
local seat_lock_hash_key = KEYS[3]
local program_id = KEYS[4]
local record_key = KEYS[5]
local record_value = KEYS[6]
local record_type = KEYS[7]

local seat_data_json = ARGV[1]
local add_seat_data_json = ARGV[2]

-- 解析座位数据
local seat_data_array = cjson.decode(seat_data_json)
local add_seat_data_array = cjson.decode(add_seat_data_json)

-- 验证和锁定座位的原子性操作
for i, seat_data in ipairs(seat_data_array) do
    local seat_id = tostring(seat_data.seatId)
    local ticket_category_id = tostring(seat_data.ticketCategoryId)
    local count = seat_data.count

    -- 构建具体的Redis Key
    local ticket_remain_key = ticket_remain_number_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_no_sold_key = seat_no_sold_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_lock_key = seat_lock_hash_key .. ":" .. program_id .. ":" .. ticket_category_id

    -- 1. 检查余票数量
    local remain_number = redis.call('hget', ticket_remain_key, ticket_category_id)
    if not remain_number or tonumber(remain_number) < count then
        return cjson.encode({code = 40001, message = "余票不足"})
    end

    -- 2. 检查座位状态
    local seat_vo_str = redis.call('hget', seat_no_sold_key, seat_id)
    if not seat_vo_str then
        return cjson.encode({code = 40002, message = "座位不存在或已被锁定"})
    end

    local seat_vo = cjson.decode(seat_vo_str)
    if seat_vo.sellStatus ~= 1 then -- 1表示未售卖
        return cjson.encode({code = 40003, message = "座位状态异常"})
    end

    -- 3. 原子性扣减余票
    redis.call('hincrby', ticket_remain_key, ticket_category_id, -count)

    -- 4. 原子性更新座位状态
    redis.call('hdel', seat_no_sold_key, seat_id)  -- 从未售卖中删除

    -- 构建锁定座位数据
    local lock_seat_data = add_seat_data_array[i]
    lock_seat_data.lockTime = redis.call('time')[1] -- 添加锁定时间戳
    redis.call('hset', seat_lock_key, seat_id, cjson.encode(lock_seat_data))  -- 添加到锁定中
end

-- 5. 记录操作日志
redis.call('hset', record_key, record_value, record_type)

return cjson.encode({code = 200, message = "操作成功"})
```

### 2.8 调用订单服务创建订单

#### 订单参数构建
```java
/**
 * 构建订单创建参数
 */
private OrderCreateDto buildCreateOrderParam(ProgramOrderCreateDto programOrderCreateDto,
                                           List<SeatVo> purchaseSeatList, Integer orderVersion) {
    OrderCreateDto orderCreateDto = new OrderCreateDto();

    // 基本信息
    orderCreateDto.setUserId(programOrderCreateDto.getUserId());
    orderCreateDto.setProgramId(programOrderCreateDto.getProgramId());
    orderCreateDto.setOrderVersion(orderVersion);

    // 生成订单号
    Long orderNumber = uidGenerator.getUid();
    orderCreateDto.setOrderNumber(orderNumber);

    // 计算总价
    BigDecimal totalPrice = calculateTotalPrice(purchaseSeatList);
    orderCreateDto.setTotalPrice(totalPrice);

    // 构建购票人信息
    List<OrderTicketUserCreateDto> orderTicketUserCreateDtoList = new ArrayList<>();
    for (int i = 0; i < purchaseSeatList.size(); i++) {
        SeatVo seatVo = purchaseSeatList.get(i);
        Long ticketUserId = programOrderCreateDto.getTicketUserIdList().get(i);

        OrderTicketUserCreateDto orderTicketUserCreateDto = new OrderTicketUserCreateDto();
        orderTicketUserCreateDto.setTicketUserId(ticketUserId);
        orderTicketUserCreateDto.setSeatId(seatVo.getId());
        orderTicketUserCreateDto.setTicketCategoryId(seatVo.getTicketCategoryId());
        orderTicketUserCreateDto.setPrice(seatVo.getPrice());

        orderTicketUserCreateDtoList.add(orderTicketUserCreateDto);
    }
    orderCreateDto.setOrderTicketUserCreateDtoList(orderTicketUserCreateDtoList);

    return orderCreateDto;
}
```

#### RPC调用订单服务
```java
/**
 * 通过RPC调用订单服务创建订单
 */
private String createOrderByRpc(OrderCreateDto orderCreateDto, List<SeatVo> purchaseSeatList) {
    try {
        // 调用订单服务
        ApiResponse<String> orderApiResponse = orderClient.create(orderCreateDto);

        if (!Objects.equals(orderApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
            log.error("调用订单服务失败，订单号：{}，错误信息：{}",
                     orderCreateDto.getOrderNumber(), orderApiResponse.getMessage());

            // 订单创建失败，需要回滚Redis缓存
            rollbackRedisCache(orderCreateDto.getProgramId(), purchaseSeatList);

            throw new DaMaiFrameException(orderApiResponse);
        }

        log.info("订单创建成功，订单号：{}", orderCreateDto.getOrderNumber());
        return orderApiResponse.getData();

    } catch (Exception e) {
        log.error("调用订单服务异常，订单号：{}", orderCreateDto.getOrderNumber(), e);

        // 异常情况下回滚Redis缓存
        rollbackRedisCache(orderCreateDto.getProgramId(), purchaseSeatList);

        throw new DaMaiFrameException(BaseCode.ORDER_CREATE_FAIL);
    }
}
```

### 2.9 订单服务处理逻辑

#### 订单创建核心逻辑
```java
/**
 * 订单服务
 * 处理订单创建的核心逻辑
 */
@Service
public class OrderService {

    /**
     * 创建订单（同步调用）
     */
    @Transactional(rollbackFor = Exception.class)
    public String create(OrderCreateDto orderCreateDto) {
        // 1. 验证订单是否已存在
        Order existOrder = orderMapper.selectOne(
            Wrappers.lambdaQuery(Order.class)
                    .eq(Order::getOrderNumber, orderCreateDto.getOrderNumber()));

        if (existOrder != null) {
            log.warn("订单已存在，订单号：{}", orderCreateDto.getOrderNumber());
            return String.valueOf(orderCreateDto.getOrderNumber());
        }

        // 2. 创建主订单
        Order order = new Order();
        BeanUtil.copyProperties(orderCreateDto, order);
        order.setOrderStatus(OrderStatus.NO_PAY.getCode());
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());

        int insertResult = orderMapper.insert(order);
        if (insertResult <= 0) {
            throw new DaMaiFrameException(BaseCode.ORDER_CREATE_FAIL);
        }

        // 3. 创建订单购票人记录
        List<OrderTicketUser> orderTicketUserList = new ArrayList<>();
        for (OrderTicketUserCreateDto ticketUserDto : orderCreateDto.getOrderTicketUserCreateDtoList()) {
            OrderTicketUser orderTicketUser = new OrderTicketUser();
            BeanUtil.copyProperties(ticketUserDto, orderTicketUser);
            orderTicketUser.setOrderNumber(orderCreateDto.getOrderNumber());
            orderTicketUser.setCreateTime(new Date());
            orderTicketUser.setUpdateTime(new Date());

            orderTicketUserList.add(orderTicketUser);
        }

        // 批量插入购票人记录
        boolean batchInsertResult = orderTicketUserService.saveBatch(orderTicketUserList);
        if (!batchInsertResult) {
            throw new DaMaiFrameException(BaseCode.ORDER_TICKET_USER_CREATE_FAIL);
        }

        log.info("订单创建成功，订单号：{}，购票人数量：{}",
                orderCreateDto.getOrderNumber(), orderTicketUserList.size());

        return String.valueOf(orderCreateDto.getOrderNumber());
    }

    /**
     * 异步创建订单（Kafka消息消费）
     */
    public String createMq(OrderCreateMq orderCreateMq) {
        // 1. 检查消息延迟时间
        long currentTimeTimestamp = System.currentTimeMillis();
        long createOrderTimeTimestamp = orderCreateMq.getCreateOrderTime();
        long delayTime = currentTimeTimestamp - createOrderTimeTimestamp;

        if (delayTime > MESSAGE_DELAY_TIME) {
            log.info("消费到kafka的创建订单消息延迟时间大于了 {} 毫秒 此订单消息被丢弃 订单号 : {}",
                    delayTime, orderCreateMq.getOrderNumber());

            // 将延迟丢弃的订单放入Redis中
            redisCache.leftPushForList(
                RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
                new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));

            return null;
        }

        // 2. 先更新节目服务的数据（V4版本策略）
        if (Objects.equals(orderCreateMq.getOrderVersion(), ProgramOrderVersion.V4_VERSION.getValue())) {
            ProgramOperateDataDto programOperateDataDto = new ProgramOperateDataDto();
            programOperateDataDto.setProgramId(orderCreateMq.getProgramId());
            programOperateDataDto.setOrderVersion(orderCreateMq.getOrderVersion());
            programOperateDataDto.setSellStatus(SellStatus.LOCK.getCode());

            ApiResponse<Boolean> programApiResponse = programClient.operateProgramData(programOperateDataDto);
            if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                // 将因为修改节目服务余票和座位失败，导致丢弃的订单放入redis中
                redisCache.leftPushForList(
                    RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
                    new DiscardOrder(orderCreateMq, DiscardOrderReason.MODIFY_PROGRAM_REMAIN_NUMBER_SEAT_FAIL.getCode()));

                throw new DaMaiFrameException(programApiResponse);
            }
        }

        // 3. 创建订单
        String orderNumber = createByMq(orderCreateMq);

        // 4. 将订单号缓存到Redis（用于查询）
        redisCache.set(RedisKeyBuild.createRedisKey(RedisKeyManage.ORDER_MQ, orderNumber),
                      orderNumber, 1, TimeUnit.MINUTES);

        return orderNumber;
    }
}
```

### 2.10 延迟队列处理

#### 延迟取消消息发送
```java
/**
 * 延迟订单取消发送器
 */
@Component
public class DelayOrderCancelSend {

    @Autowired
    private DelayQueueContext delayQueueContext;

    /**
     * 发送延迟取消消息
     * 10分钟后自动取消未支付订单
     */
    public void sendMessage(String message) {
        try {
            log.info("延迟订单取消消息进行发送 消息体 : {}", message);

            delayQueueContext.sendMessage(
                SpringUtil.getPrefixDistinctionName() + "-" + DELAY_ORDER_CANCEL_TOPIC,
                message,
                DELAY_ORDER_CANCEL_TIME,      // 10分钟
                DELAY_ORDER_CANCEL_TIME_UNIT  // 时间单位：分钟
            );
        } catch (Exception e) {
            log.error("send message error message : {}", message, e);
        }
    }
}
```

#### 延迟队列消费处理
```java
/**
 * 延迟订单取消消费者
 */
@Component
public class DelayOrderCancelConsumer implements ConsumerTask {

    @Autowired
    private OrderService orderService;

    @Override
    public void execute(String content) {
        log.info("延迟订单取消消息进行消费 content : {}", content);

        if (StringUtil.isEmpty(content)) {
            log.error("延迟队列消息不存在");
            return;
        }

        try {
            // 解析延迟消息
            DelayOrderCancelDto delayOrderCancelDto = JSON.parseObject(content, DelayOrderCancelDto.class);

            // 执行订单取消逻辑
            OrderCancelDto orderCancelDto = new OrderCancelDto();
            orderCancelDto.setOrderNumber(delayOrderCancelDto.getOrderNumber());

            boolean cancel = orderService.cancel(orderCancelDto);
            if (cancel) {
                log.info("延迟订单取消成功 orderCancelDto : {}", content);
            } else {
                log.error("延迟订单取消失败 orderCancelDto : {}", content);
            }
        } catch (Exception e) {
            log.error("处理延迟订单取消消息失败", e);
        }
    }

    @Override
    public String topic() {
        return SpringUtil.getPrefixDistinctionName() + "-" + DELAY_ORDER_CANCEL_TOPIC;
    }
}
```

#### 订单取消逻辑
```java
/**
 * 订单取消处理
 */
@RepeatExecuteLimit(name = CANCEL_PROGRAM_ORDER, keys = {"#orderCancelDto.orderNumber"})
@ServiceLock(name = ORDER_CANCEL_LOCK, keys = {"#orderCancelDto.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public boolean cancel(OrderCancelDto orderCancelDto) {
    // 1. 查询订单信息
    Order order = orderMapper.selectOne(
        Wrappers.lambdaQuery(Order.class)
                .eq(Order::getOrderNumber, orderCancelDto.getOrderNumber()));

    if (Objects.isNull(order)) {
        log.warn("订单不存在，订单号：{}", orderCancelDto.getOrderNumber());
        return false;
    }

    // 2. 检查订单状态
    if (!Objects.equals(order.getOrderStatus(), OrderStatus.NO_PAY.getCode())) {
        log.warn("订单状态不允许取消，订单号：{}，当前状态：{}",
                orderCancelDto.getOrderNumber(), order.getOrderStatus());
        return false;
    }

    // 3. 更新订单状态为已取消
    updateOrderRelatedData(orderCancelDto.getOrderNumber(), OrderStatus.CANCEL);

    // 4. 回滚Redis缓存（释放锁定的座位和恢复库存）
    rollbackProgramCache(order);

    log.info("订单取消成功，订单号：{}", orderCancelDto.getOrderNumber());
    return true;
}

/**
 * 回滚节目相关缓存数据
 */
private void rollbackProgramCache(Order order) {
    // 查询订单的座位信息
    List<OrderTicketUser> orderTicketUserList = orderTicketUserMapper.selectList(
        Wrappers.lambdaQuery(OrderTicketUser.class)
                .eq(OrderTicketUser::getOrderNumber, order.getOrderNumber()));

    if (CollectionUtil.isEmpty(orderTicketUserList)) {
        return;
    }

    // 构建回滚参数
    Map<Long, List<Long>> seatMap = orderTicketUserList.stream()
        .collect(Collectors.groupingBy(
            OrderTicketUser::getTicketCategoryId,
            Collectors.mapping(OrderTicketUser::getSeatId, Collectors.toList())));

    // 调用节目服务回滚缓存
    updateProgramRelatedDataResolution(
        order.getProgramId(),
        seatMap,
        OrderStatus.CANCEL,
        order.getId(),
        order.getUserId(),
        null,
        order.getOrderVersion());
}
```

## 三、异常处理和回滚机制

### 3.1 Redis缓存回滚

#### 座位状态回滚
```java
/**
 * Redis缓存回滚处理
 */
private void rollbackRedisCache(Long programId, List<SeatVo> purchaseSeatList) {
    try {
        log.info("开始回滚Redis缓存，节目ID：{}，座位数量：{}", programId, purchaseSeatList.size());

        // 构建回滚参数
        List<String> keys = new ArrayList<>();
        Object[] data = new Object[10];

        // 构建回滚座位数据
        JSONArray rollbackSeatArray = new JSONArray();
        for (SeatVo seatVo : purchaseSeatList) {
            JSONObject rollbackSeat = new JSONObject();
            rollbackSeat.put("seatId", seatVo.getId());
            rollbackSeat.put("ticketCategoryId", seatVo.getTicketCategoryId());
            rollbackSeat.put("sellStatus", SellStatus.NO_SOLD.getCode());
            rollbackSeat.put("count", 1);
            rollbackSeatArray.add(rollbackSeat);
        }

        // 构建Redis Key
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION));
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH));
        keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH));
        keys.add(String.valueOf(programId));

        data[0] = JSON.toJSONString(rollbackSeatArray);

        // 执行回滚Lua脚本
        String result = programSeatRollbackCacheData.rollbackData(keys, data);

        if (StringUtil.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (Objects.equals(code, BaseCode.SUCCESS.getCode())) {
                log.info("Redis缓存回滚成功，节目ID：{}", programId);
            } else {
                log.error("Redis缓存回滚失败，节目ID：{}，错误信息：{}",
                         programId, jsonObject.getString("message"));
            }
        }

    } catch (Exception e) {
        log.error("Redis缓存回滚异常，节目ID：{}", programId, e);
    }
}
```

#### 回滚Lua脚本
```lua
-- 座位状态回滚Lua脚本 (programSeatRollback.lua)
local ticket_remain_number_hash_key = KEYS[1]
local seat_no_sold_hash_key = KEYS[2]
local seat_lock_hash_key = KEYS[3]
local program_id = KEYS[4]

local rollback_seat_data_json = ARGV[1]

-- 解析回滚座位数据
local rollback_seat_data_array = cjson.decode(rollback_seat_data_json)

-- 执行回滚操作
for i, seat_data in ipairs(rollback_seat_data_array) do
    local seat_id = tostring(seat_data.seatId)
    local ticket_category_id = tostring(seat_data.ticketCategoryId)
    local count = seat_data.count

    -- 构建具体的Redis Key
    local ticket_remain_key = ticket_remain_number_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_no_sold_key = seat_no_sold_hash_key .. ":" .. program_id .. ":" .. ticket_category_id
    local seat_lock_key = seat_lock_hash_key .. ":" .. program_id .. ":" .. ticket_category_id

    -- 1. 恢复余票数量
    redis.call('hincrby', ticket_remain_key, ticket_category_id, count)

    -- 2. 从锁定状态中删除座位
    local locked_seat_str = redis.call('hget', seat_lock_key, seat_id)
    if locked_seat_str then
        redis.call('hdel', seat_lock_key, seat_id)

        -- 3. 将座位恢复到未售卖状态
        local seat_data_obj = cjson.decode(locked_seat_str)
        seat_data_obj.sellStatus = 1  -- 1表示未售卖
        seat_data_obj.lockTime = nil
        seat_data_obj.userId = nil

        redis.call('hset', seat_no_sold_key, seat_id, cjson.encode(seat_data_obj))
    end
end

return cjson.encode({code = 200, message = "回滚成功"})
```

### 3.2 分布式锁释放

#### 锁释放机制
```java
/**
 * 分布式锁释放处理
 */
public class LockReleaseHandler {

    /**
     * 安全释放分布式锁
     */
    public void safeReleaseLock(RLock lock, String lockName) {
        try {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("分布式锁释放成功，锁名称：{}", lockName);
            }
        } catch (Exception e) {
            log.error("分布式锁释放异常，锁名称：{}", lockName, e);
        }
    }

    /**
     * 批量释放锁
     */
    public void batchReleaseLocks(Map<String, RLock> lockMap) {
        lockMap.forEach((lockName, lock) -> {
            safeReleaseLock(lock, lockName);
        });
    }
}
```

### 3.3 异常监控和告警

#### 购票异常监控
```java
/**
 * 购票异常监控服务
 */
@Service
public class TicketPurchaseMonitor {

    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);
    private final AtomicLong rollbackCount = new AtomicLong(0);

    /**
     * 记录购票结果
     */
    public void recordPurchaseResult(String result, String reason) {
        switch (result) {
            case "SUCCESS":
                successCount.incrementAndGet();
                break;
            case "FAILURE":
                failureCount.incrementAndGet();
                log.warn("购票失败，原因：{}", reason);
                break;
            case "ROLLBACK":
                rollbackCount.incrementAndGet();
                log.warn("购票回滚，原因：{}", reason);
                break;
        }
    }

    /**
     * 定期输出统计信息
     */
    @Scheduled(fixedRate = 60000) // 每分钟输出一次
    public void reportStatistics() {
        long total = successCount.get() + failureCount.get();
        double successRate = total > 0 ? (double) successCount.get() / total * 100 : 0.0;

        log.info("购票统计 - 成功：{}，失败：{}，回滚：{}，成功率：{:.2f}%",
                successCount.get(), failureCount.get(), rollbackCount.get(), successRate);

        // 成功率过低时发送告警
        if (total > 100 && successRate < 80.0) {
            sendLowSuccessRateAlert(successRate);
        }

        // 重置计数器
        successCount.set(0);
        failureCount.set(0);
        rollbackCount.set(0);
    }

    /**
     * 发送成功率过低告警
     */
    private void sendLowSuccessRateAlert(double successRate) {
        String alertMessage = String.format("购票成功率过低：%.2f%%，请检查系统状态", successRate);
        // 发送到监控系统
        monitoringService.sendAlert("LOW_TICKET_PURCHASE_SUCCESS_RATE", alertMessage);
    }
}
```

## 四、性能优化策略

### 4.1 并发控制优化

#### 分段锁策略
```java
/**
 * 分段锁优化
 * 将节目级别的锁细化到票档级别
 */
@Service
public class OptimizedProgramOrderService {

    /**
     * 使用票档级别的分布式锁
     * 提高并发度
     */
    @ServiceLock(
        name = PROGRAM_ORDER_CREATE_OPTIMIZED,
        keys = {"#programOrderCreateDto.programId", "#programOrderCreateDto.ticketCategoryId"},
        lockType = LockType.Reentrant
    )
    public String createOrderOptimized(ProgramOrderCreateDto programOrderCreateDto) {
        // 购票逻辑
        return doCreateOrder(programOrderCreateDto);
    }

    /**
     * 异步处理非关键路径
     */
    private String doCreateOrder(ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 同步处理关键路径：座位锁定、库存扣减
        String identifierId = lockSeatsAndReduceInventory(programOrderCreateDto);

        // 2. 异步处理非关键路径：订单创建、消息发送
        CompletableFuture.runAsync(() -> {
            try {
                createOrderAsync(programOrderCreateDto, identifierId);
            } catch (Exception e) {
                log.error("异步创建订单失败", e);
                // 异步失败时的补偿机制
                handleAsyncOrderCreationFailure(programOrderCreateDto, identifierId);
            }
        });

        return identifierId;
    }
}
```

### 4.2 缓存预热策略

#### 热门节目缓存预热
```java
/**
 * 缓存预热服务
 */
@Service
public class CacheWarmupService {

    /**
     * 节目开售前缓存预热
     */
    @Scheduled(cron = "0 */10 * * * ?") // 每10分钟执行一次
    public void warmupHotPrograms() {
        // 1. 获取即将开售的热门节目
        List<Program> hotPrograms = getUpcomingSalePrograms();

        for (Program program : hotPrograms) {
            try {
                // 2. 预热节目基本信息
                warmupProgramInfo(program.getId());

                // 3. 预热座位信息
                warmupSeatInfo(program.getId());

                // 4. 预热票档信息
                warmupTicketCategoryInfo(program.getId());

                log.info("节目缓存预热完成，节目ID：{}", program.getId());

            } catch (Exception e) {
                log.error("节目缓存预热失败，节目ID：{}", program.getId(), e);
            }
        }
    }

    /**
     * 预热座位信息
     */
    private void warmupSeatInfo(Long programId) {
        // 查询所有票档
        List<TicketCategory> ticketCategories = ticketCategoryMapper.selectList(
            Wrappers.lambdaQuery(TicketCategory.class)
                    .eq(TicketCategory::getProgramId, programId));

        for (TicketCategory ticketCategory : ticketCategories) {
            // 预加载座位信息到缓存
            seatService.selectSeatResolution(programId, ticketCategory.getId(),
                                           3600L, TimeUnit.SECONDS);
        }
    }
}
```

## 五、总结

### 5.1 购票流程核心特点

#### 5.1.1 多重保护机制
1. **幂等性保护**：防止重复提交，60秒内同一用户同一节目只能提交一次
2. **分布式锁保护**：保证同一节目的购票操作串行执行，避免超卖
3. **原子性操作**：使用Lua脚本保证Redis操作的原子性
4. **事务保护**：数据库操作使用事务保证数据一致性

#### 5.1.2 高性能设计
1. **多级缓存**：本地缓存→Redis缓存→数据库的三级查询
2. **异步处理**：非关键路径异步处理，提高响应速度
3. **分段锁**：细化锁粒度，提高并发度
4. **缓存预热**：热门节目提前预热缓存

#### 5.1.3 可靠性保障
1. **自动回滚**：异常情况下自动回滚Redis缓存和释放锁
2. **延迟队列**：10分钟后自动取消未支付订单
3. **监控告警**：实时监控购票成功率和异常情况
4. **补偿机制**：异步处理失败时的补偿处理

### 5.2 技术亮点

#### 5.2.1 组合模式验证链
- **灵活扩展**：新增验证规则只需添加新组件
- **层次清晰**：树形结构直观展示验证流程
- **职责单一**：每个验证组件职责明确

#### 5.2.2 Lua脚本原子操作
- **原子性保证**：多个Redis操作在一个脚本中执行
- **性能优化**：减少网络往返次数
- **一致性保障**：避免并发操作导致的数据不一致

#### 5.2.3 版本化处理策略
- **渐进式升级**：支持多个版本的购票流程并存
- **A/B测试**：可以对比不同版本的性能表现
- **平滑迁移**：新版本出问题时可以快速回退

### 5.3 适用场景

这套购票流程设计特别适用于：
- **高并发场景**：明星演唱会、热门演出等
- **强一致性要求**：不能出现超卖情况
- **用户体验要求高**：响应时间要快，操作要流畅
- **业务复杂度高**：涉及多个服务协调，需要保证数据一致性

通过精心设计的多重保护机制和高性能架构，大麦购票系统能够在高并发场景下稳定运行，为用户提供流畅的购票体验。
```
```
