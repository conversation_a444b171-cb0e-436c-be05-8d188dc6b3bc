# 大麦项目Kafka消息队列详细分析

## 1. 为什么选择Kafka，而不是RocketMQ？

### 1.1 技术选型分析

根据项目代码分析，选择Kafka的主要原因：

**1. 高吞吐量需求**
- 大麦购票系统面临高并发场景，需要处理大量订单创建消息
- Kafka在高吞吐量场景下表现优异，单机可达百万级TPS

**2. 简化架构**
- 项目采用Spring Boot + Spring Cloud微服务架构
- Spring Kafka提供了完善的集成支持，配置简单
- 减少了额外的中间件依赖和运维复杂度

**3. 消息特性匹配**
- 订单创建场景主要是异步处理，对消息顺序要求不严格
- 更注重吞吐量和可用性，而非强一致性

**4. 成本考虑**
- Kafka开源免费，运维成本相对较低
- 社区活跃，文档完善，学习成本低

### 1.2 配置示例

<augment_code_snippet path="damai-server/damai-program-service/src/main/resources/application.yml" mode="EXCERPT">
````yaml
spring:
  kafka:
    bootstrap-servers: 127.0.0.1:9092
    producer:
      retries: 1
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    topic: create_order
````
</augment_code_snippet>

## 2. 消息结构设计

### 2.1 核心消息对象

**OrderCreateMq** - 订单创建消息结构：

```java
public class OrderCreateMq {
    private String orderNumber;           // 订单号
    private Long programId;              // 节目ID
    private Long userId;                 // 用户ID
    private Date createOrderTime;        // 订单创建时间
    private List<OrderTicketUserCreateDto> orderTicketUserCreateDtoList; // 购票人信息
    private BigDecimal orderPrice;       // 订单价格
    // 其他字段...
}
```

**ApiData** - API调用记录消息：

```java
public class ApiData {
    private Long id;                     // 记录ID
    private String apiPath;              // API路径
    private String requestData;          // 请求数据
    private String responseData;         // 响应数据
    private Date createTime;             // 创建时间
    // 其他字段...
}
```

### 2.2 Topic设计

项目采用**环境前缀 + 业务主题**的命名方式：

<augment_code_snippet path="damai-server/damai-program-service/src/main/java/com/damai/service/kafka/CreateOrderSend.java" mode="EXCERPT">
````java
public void sendMessage(String message, SuccessCallback<SendResult<String, String>> successCallback, 
                        FailureCallback failureCallback) {
    log.info("创建订单kafka发送消息 消息体 : {}", message);
    CompletableFuture<SendResult<String, String>> completableFuture = 
            kafkaTemplate.send(SpringUtil.getPrefixDistinctionName() + "-" + kafkaTopic.getTopic(), message);
}
````
</augment_code_snippet>

**Topic命名规则：**
- `{环境前缀}-create_order`：订单创建主题
- `{环境前缀}-save_api_data`：API数据保存主题

## 3. 消息重复消费解决方案

### 3.1 幂等性保证机制

**1. 基于注解的幂等性控制**

<augment_code_snippet path="damai-server/damai-order-service/src/main/java/com/damai/service/OrderService.java" mode="EXCERPT">
````java
@RepeatExecuteLimit(name = CREATE_PROGRAM_ORDER_MQ, keys = {"#orderCreateMq.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public String createMq(OrderCreateMq orderCreateMq) {
    // 订单创建逻辑
}
````
</augment_code_snippet>

**2. 数据库唯一约束**

<augment_code_snippet path="damai-server/damai-order-service/src/main/java/com/damai/service/OrderService.java" mode="EXCERPT">
````java
@Transactional(rollbackFor = Exception.class)
public String doCreate(OrderCreateDomain orderCreateDomain) {
    LambdaQueryWrapper<Order> orderLambdaQueryWrapper =
            Wrappers.lambdaQuery(Order.class).eq(Order::getOrderNumber, orderCreateDomain.getOrderNumber());
    //如果订单存在了，那么直接拒绝
    Order oldOrder = orderMapper.selectOne(orderLambdaQueryWrapper);
    if (Objects.nonNull(oldOrder)) {
        throw new DaMaiFrameException(BaseCode.ORDER_EXIST);
    }
}
````
</augment_code_snippet>

**3. API数据保存幂等性**

<augment_code_snippet path="damai-server/damai-customize-service/src/main/java/com/damai/service/ApiDataService.java" mode="EXCERPT">
````java
@RepeatExecuteLimit(name = RepeatExecuteLimitConstants.CONSUMER_API_DATA_MESSAGE, keys = {"#apiData.id"})
public void saveApiData(ApiData apiData) {
    ApiData dbApiData = apiDataMapper.selectById(apiData.getId());
    if (Objects.isNull(dbApiData)) {
        log.info("saveApiData apiData:{}", JSON.toJSONString(apiData));
        apiDataMapper.insert(apiData);
    }
}
````
</augment_code_snippet>

### 3.2 幂等性实现原理

1. **分布式锁 + Redis标记**：通过Redis存储执行标记，防止重复执行
2. **业务主键唯一性**：订单号、API记录ID等业务主键保证唯一性
3. **数据库约束**：利用数据库唯一约束防止重复插入

## 4. 消息积压处理策略

### 4.1 消息延迟检测

<augment_code_snippet path="damai-server/damai-order-service/src/main/java/com/damai/service/kafka/CreateOrderConsumer.java" mode="EXCERPT">
````java
@KafkaListener(topics = {SPRING_INJECT_PREFIX_DISTINCTION_NAME+"-"+"${spring.kafka.topic:create_order}"})
public void consumerOrderMessage(ConsumerRecord<String,String> consumerRecord) {
    OrderCreateMq orderCreateMq = JSON.parseObject(value, OrderCreateMq.class);
    
    long createOrderTimeTimestamp = orderCreateMq.getCreateOrderTime().getTime();
    long currentTimeTimestamp = System.currentTimeMillis();
    long delayTime = currentTimeTimestamp - createOrderTimeTimestamp;
    
    log.info("消费到kafka的创建订单消息 消息体: {} 延迟时间 : {} 毫秒", value, delayTime);
}
````
</augment_code_snippet>

### 4.2 积压处理机制

**1. 消息超时丢弃**

```java
// 设置消息延迟阈值（如5秒）
private static final long MESSAGE_DELAY_TIME = 5000;

if (delayTime > MESSAGE_DELAY_TIME) {
    log.info("消费到kafka的创建订单消息延迟时间大于了 {} 毫秒 此订单消息被丢弃", delayTime);
    // 将延迟丢弃的订单放入Redis补偿队列
    redisCache.leftPushForList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
        new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));
}
```

**2. 补偿队列机制**

- 超时消息不直接丢弃，而是放入Redis补偿队列
- 后续通过定时任务或人工干预处理补偿队列中的消息
- 保证重要业务数据不丢失

### 4.3 消费者配置优化

<augment_code_snippet path="damai-server/damai-order-service/src/main/resources/application.yml" mode="EXCERPT">
````yaml
spring:
  kafka:
    consumer:
      group-id: create_order_data
      enable-auto-commit: true
      auto-commit-interval: 200
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
````
</augment_code_snippet>

## 5. 消息有序性保证

### 5.1 项目中的有序性需求

根据代码分析，项目对消息有序性要求不严格：

1. **订单创建消息**：每个订单独立处理，无严格顺序要求
2. **API数据记录**：主要用于统计分析，对顺序不敏感

### 5.2 有序性实现方案（如需要）

如果需要保证有序性，可以采用以下方案：

**1. 单分区发送**
```java
// 指定分区发送，保证同一分区内有序
kafkaTemplate.send(topic, partition, key, message);
```

**2. 相同Key路由**
```java
// 使用订单号或用户ID作为Key，确保相同Key的消息发送到同一分区
kafkaTemplate.send(topic, orderNumber, message);
```

**3. 单线程消费**
```java
// 配置单线程消费者，保证消费顺序
@KafkaListener(topics = "order_topic", concurrency = "1")
```

## 6. 消息发送模式分析

### 6.1 发送模式

项目采用**异步发送**模式：

<augment_code_snippet path="damai-server/damai-program-service/src/main/java/com/damai/service/kafka/CreateOrderSend.java" mode="EXCERPT">
````java
public void sendMessage(String message, SuccessCallback<SendResult<String, String>> successCallback, 
                        FailureCallback failureCallback) {
    CompletableFuture<SendResult<String, String>> completableFuture = 
            kafkaTemplate.send(SpringUtil.getPrefixDistinctionName() + "-" + kafkaTopic.getTopic(), message);
    completableFuture.whenComplete((result,ex) -> {
        if (Objects.isNull(ex)) {
            successCallback.onSuccess(result);
        }else {
            failureCallback.onFailure(ex);
        }
    });
}
````
</augment_code_snippet>

### 6.2 Producer配置

<augment_code_snippet path="damai-server/damai-program-service/src/main/resources/application.yml" mode="EXCERPT">
````yaml
spring:
  kafka:
    producer:
      retries: 1                    # 重试次数
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
````
</augment_code_snippet>

**特点分析：**
- **异步发送**：提高发送性能，不阻塞主线程
- **回调处理**：通过CompletableFuture处理发送结果
- **重试机制**：配置retries=1，发送失败时重试一次

## 7. 消息可靠性保证

### 7.1 消息不丢失机制

**1. Producer端保证**
```yaml
spring:
  kafka:
    producer:
      retries: 1                    # 发送失败重试
      acks: 1                       # 等待leader确认（默认）
```

**2. Consumer端保证**
```yaml
spring:
  kafka:
    consumer:
      enable-auto-commit: true      # 自动提交offset
      auto-commit-interval: 200     # 提交间隔200ms
```

**3. 业务层保证**
- 消息消费失败时记录日志，便于排查
- 重要消息失败后放入补偿队列
- 通过数据库事务保证数据一致性

### 7.2 消费幂等性保证

**1. 注解级幂等性**
```java
@RepeatExecuteLimit(name = CREATE_PROGRAM_ORDER_MQ, keys = {"#orderCreateMq.orderNumber"})
```

**2. 业务级幂等性**
```java
// 检查订单是否已存在
Order oldOrder = orderMapper.selectOne(orderLambdaQueryWrapper);
if (Objects.nonNull(oldOrder)) {
    throw new DaMaiFrameException(BaseCode.ORDER_EXIST);
}
```

**3. 数据库约束幂等性**
- 订单号唯一约束
- API记录ID唯一约束

## 8. 失败处理和补偿机制

### 8.1 消费失败重试策略

项目采用**业务层重试 + 补偿队列**的策略：

**1. 消费异常处理**

<augment_code_snippet path="damai-server/damai-order-service/src/main/java/com/damai/service/kafka/CreateOrderConsumer.java" mode="EXCERPT">
````java
@KafkaListener(topics = {SPRING_INJECT_PREFIX_DISTINCTION_NAME+"-"+"${spring.kafka.topic:create_order}"})
public void consumerOrderMessage(ConsumerRecord<String,String> consumerRecord) {
    try {
        // 消息处理逻辑
        String orderNumber = orderService.createMq(orderCreateMq);
        log.info("消费到kafka的创建订单消息 创建订单成功 订单号 : {}", orderNumber);
    } catch (Exception e) {
        log.error("处理消费到kafka的创建订单消息失败 error", e);
    }
}
````
</augment_code_snippet>

### 8.2 死信队列（DLQ）设计

项目采用**Redis补偿队列**替代传统死信队列：

**1. 补偿队列结构**

<augment_code_snippet path="damai-server/damai-order-service/src/main/java/com/damai/domain/DiscardOrder.java" mode="EXCERPT">
````java
@Data
public class DiscardOrder {
    private OrderCreateMq orderCreateMq;        // 原始订单消息
    private Integer discardOrderReason;         // 丢弃原因
}
````
</augment_code_snippet>

**2. 丢弃原因枚举**

<augment_code_snippet path="damai-common/src/main/java/com/damai/enums/DiscardOrderReason.java" mode="EXCERPT">
````java
public enum DiscardOrderReason {
    CONSUMER_DELAY(1, "延时"),
    MODIFY_PROGRAM_REMAIN_NUMBER_SEAT_FAIL(2, "修改节目服务的余票和座位失败");
}
````
</augment_code_snippet>

### 8.3 补偿处理流程

**1. 消息延迟补偿**
```java
if (delayTime > MESSAGE_DELAY_TIME) {
    redisCache.leftPushForList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
        new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));
}
```

**2. 业务失败补偿**
```java
if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
    redisCache.leftPushForList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
        new DiscardOrder(orderCreateMq, DiscardOrderReason.MODIFY_PROGRAM_REMAIN_NUMBER_SEAT_FAIL.getCode()));
    throw new DaMaiFrameException(programApiResponse);
}
```

### 8.4 落库失败补偿机制

**1. 事务回滚保证**
```java
@Transactional(rollbackFor = Exception.class)
public String createMq(OrderCreateMq orderCreateMq) {
    // 数据库操作失败时自动回滚
}
```

**2. 分布式事务保证**
- 使用Seata分布式事务框架
- 保证跨服务调用的数据一致性

**3. 补偿任务机制**
- 定时扫描补偿队列
- 人工干预处理异常订单
- 数据对账和修复机制

## 总结

大麦项目的Kafka消息队列设计体现了以下特点：

1. **高性能**：异步发送，高吞吐量处理
2. **高可靠**：多层幂等性保证，事务一致性
3. **容错性**：完善的补偿机制，异常处理
4. **可监控**：详细的日志记录，便于问题排查
5. **可扩展**：模块化设计，易于扩展新的消息类型

这套设计在保证消息可靠性的同时，最大化了系统的性能和可用性，为高并发购票场景提供了稳定的技术支撑。
