# 大麦项目鉴权机制详细分析

## 1. 鉴权架构概述

大麦项目采用多层次的鉴权机制，通过网关统一处理所有请求的身份验证和权限控制。整个鉴权体系包括：

- **渠道验证**：基于应用渠道码的多租户隔离
- **RSA签名验证**：防止请求篡改和重放攻击
- **JWT Token验证**：用户身份认证
- **API限流控制**：防止恶意请求和系统过载
- **路径权限控制**：基于URL路径的细粒度权限管理

## 2. 鉴权流程架构图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant ChannelService as 渠道服务
    participant TokenService as Token服务
    participant UserService as 用户服务
    participant Redis as Redis缓存
    
    Client->>Gateway: 发送请求(code, token, sign)
    Gateway->>Gateway: 限流检查
    Gateway->>ChannelService: 获取渠道配置
    ChannelService->>Redis: 查询渠道缓存
    Redis-->>ChannelService: 返回渠道配置
    Gateway->>Gateway: RSA签名验证
    Gateway->>Gateway: 检查是否需要Token验证
    alt 需要Token验证
        Gateway->>TokenService: 验证Token
        TokenService->>Redis: 查询用户缓存
        Redis-->>TokenService: 返回用户信息
        TokenService-->>Gateway: 返回用户ID
    end
    Gateway->>UserService: 转发请求
    UserService-->>Gateway: 返回响应
    Gateway-->>Client: 返回最终响应
```

## 3. 核心组件详解

### 3.1 网关请求验证过滤器 (RequestValidationFilter)

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/filter/RequestValidationFilter.java" mode="EXCERPT">
````java
@Component
public class RequestValidationFilter implements GlobalFilter, Ordered {
    
    @Override
    public Mono<Void> filter(final ServerWebExchange exchange, final GatewayFilterChain chain) {
        // 1. 限流检查
        if (rateLimiterProperty.getRateSwitch()) {
            try {
                rateLimiter.acquire();
                return doFilter(exchange, chain);
            } catch (InterruptedException e) {
                throw new DaMaiFrameException(BaseCode.THREAD_INTERRUPTED);
            } finally {
                rateLimiter.release();
            }
        } else {
            return doFilter(exchange, chain);
        }
    }
    
    private Map<String,String> doExecute(String originalBody, ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        Map<String, String> bodyContent = JSON.parseObject(originalBody, Map.class);
        String url = request.getPath().value();
        
        // 2. 渠道验证
        String code = bodyContent.get(CODE);
        GetChannelDataVo channelDataVo = channelDataService.getChannelDataByCode(code);
        
        // 3. RSA签名验证
        boolean checkFlag = RsaSignTool.verifyRsaSign256(bodyContent, channelDataVo.getSignPublicKey());
        if (!checkFlag) {
            throw new DaMaiFrameException(BaseCode.RSA_SIGN_ERROR);
        }
        
        // 4. Token验证
        String token = request.getHeaders().getFirst(TOKEN);
        boolean skipCheckTokenResult = skipCheckToken(url);
        if (!skipCheckTokenResult) {
            UserVo userVo = tokenService.getUser(token, code, channelDataVo.getTokenSecret());
            userId = userVo.getId();
        }
        
        return resultMap;
    }
}
````
</augment_code_snippet>

### 3.2 渠道数据服务 (ChannelDataService)

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/service/ChannelDataService.java" mode="EXCERPT">
````java
@Service
public class ChannelDataService {
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private BaseDataClient baseDataClient;
    
    /**
     * 根据渠道码获取渠道配置
     */
    public GetChannelDataVo getChannelDataByCode(String code) {
        checkCode(code);
        // 1. 先从Redis缓存获取
        GetChannelDataVo channelDataVo = getChannelDataByRedis(code);
        if (Objects.isNull(channelDataVo)) {
            // 2. 缓存未命中，从数据库获取
            channelDataVo = getChannelDataByClient(code);
            // 3. 设置Redis缓存
            setChannelDataRedis(code, channelDataVo);
        }
        return channelDataVo;
    }
    
    private GetChannelDataVo getChannelDataByRedis(String code) {
        return redisCache.get(RedisKeyBuild.createRedisKey(RedisKeyManage.CHANNEL_DATA, code), 
                             GetChannelDataVo.class);
    }
    
    private GetChannelDataVo getChannelDataByClient(String code) {
        GetChannelDataByCodeDto dto = new GetChannelDataByCodeDto();
        dto.setCode(code);
        ApiResponse<GetChannelDataVo> response = baseDataClient.getByCode(dto);
        if (Objects.equals(response.getCode(), BaseCode.SUCCESS.getCode())) {
            return response.getData();
        }
        throw new DaMaiFrameException("没有找到ChannelData");
    }
}
````
</augment_code_snippet>

### 3.3 Token服务 (TokenService)

**网关层Token服务：**

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/service/TokenService.java" mode="EXCERPT">
````java
@Component
public class TokenService {
    
    @Autowired
    private RedisCache redisCache;
    
    /**
     * 解析Token获取用户ID
     */
    public String parseToken(String token, String tokenSecret) {
        String userStr = TokenUtil.parseToken(token, tokenSecret);
        if (StringUtil.isNotEmpty(userStr)) {
            return JSONObject.parseObject(userStr).getString("userId");
        }
        return null;
    }
    
    /**
     * 根据Token获取用户信息
     */
    public UserVo getUser(String token, String code, String tokenSecret) {
        UserVo userVo = null;
        String userId = parseToken(token, tokenSecret);
        if (StringUtil.isNotEmpty(userId)) {
            // 从Redis获取用户缓存，key包含渠道码实现多租户隔离
            userVo = redisCache.get(
                RedisKeyBuild.createRedisKey(RedisKeyManage.USER_LOGIN, code, userId), 
                UserVo.class);
        }
        return Optional.ofNullable(userVo)
                .orElseThrow(() -> new DaMaiFrameException(BaseCode.LOGIN_USER_NOT_EXIST));
    }
}
````
</augment_code_snippet>

### 3.4 JWT Token工具类

<augment_code_snippet path="damai-common/src/main/java/com/damai/jwt/TokenUtil.java" mode="EXCERPT">
````java
@Slf4j
public class TokenUtil {
    
    private static final SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS256;
    
    /**
     * 创建Token
     */
    public static String createToken(String id, String info, long ttlMillis, String tokenSecret) {
        long nowMillis = System.currentTimeMillis();
        
        JwtBuilder builder = Jwts.builder()
                .setId(id)
                .setIssuedAt(new Date(nowMillis))
                .setSubject(info)
                .signWith(SIGNATURE_ALGORITHM, tokenSecret);
                
        if (ttlMillis >= 0) {
            // 设置过期时间
            builder.setExpiration(new Date(nowMillis + ttlMillis));
        }
        return builder.compact();
    }
    
    /**
     * 解析Token
     */
    public static String parseToken(String token, String tokenSecret) {
        try {
            return Jwts.parser()
                    .setSigningKey(tokenSecret)
                    .parseClaimsJws(token)
                    .getBody()
                    .getSubject();
        } catch (ExpiredJwtException jwtException) {
            log.error("parseToken error", jwtException);
            throw new DaMaiFrameException(BaseCode.TOKEN_EXPIRE);
        }
    }
}
````
</augment_code_snippet>

## 4. RSA签名验证机制

### 4.1 签名验证工具

<augment_code_snippet path="damai-common/src/main/java/com/damai/util/RsaSignTool.java" mode="EXCERPT">
````java
public class RsaSignTool {
    
    private static final String SIGN_TYPE = "RSA";
    private static final String CHARSET = "UTF-8";
    
    /**
     * RSA签名
     */
    public static String rsaSign256(String content, String privateKey) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(SIGN_TYPE);
            Signature si = Signature.getInstance(SignAlgorithm.SHA256withRSA.getValue());
            si.initSign(keyFactory.generatePrivate(keySpec));
            si.update(content.getBytes(CHARSET));
            byte[] sign = si.sign();
            return Base64.getEncoder().encodeToString(sign);
        } catch (Exception e) {
            throw new DaMaiFrameException(BaseCode.GENERATE_RSA_SIGN_ERROR);
        }
    }
    
    /**
     * 验证RSA签名
     */
    public static boolean verifyRsaSign256(Map<String, String> params, String publicKey) {
        try {
            String sign = params.get("sign");
            String content = getSignCheckContent(params);
            return verifyRsaSign256(content.getBytes(CHARSET), sign, publicKey);
        } catch (Exception e) {
            throw new DaMaiFrameException(BaseCode.RSA_SIGN_ERROR);
        }
    }
    
    public static boolean verifyRsaSign256(byte[] dataBytes, String sign, String publicKey) 
            throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        byte[] signByte = Base64.getDecoder().decode(sign);
        byte[] encodedKey = Base64.getDecoder().decode(publicKey);
        Signature signature = Signature.getInstance(SignAlgorithm.SHA256withRSA.getValue());
        KeyFactory keyFac = KeyFactory.getInstance(SIGN_TYPE);
        PublicKey puk = keyFac.generatePublic(new X509EncodedKeySpec(encodedKey));
        signature.initVerify(puk);
        signature.update(dataBytes);
        return signature.verify(signByte);
    }
}
````
</augment_code_snippet>

## 5. 用户登录和Token生成

### 5.1 用户登录流程

<augment_code_snippet path="damai-server/damai-user-service/src/main/java/com/damai/service/UserService.java" mode="EXCERPT">
````java
@Service
public class UserService {
    
    @Value("${token.expire.time:30}")
    private Integer tokenExpireTime;
    
    /**
     * 用户登录
     */
    public UserLoginVo login(UserLoginDto userLoginDto) {
        String code = userLoginDto.getCode();
        Long userId = userLoginDto.getUserId();
        String password = userLoginDto.getPassword();
        
        UserLoginVo userLoginVo = new UserLoginVo();
        
        // 1. 验证用户名密码
        LambdaQueryWrapper<User> queryUserWrapper = Wrappers.lambdaQuery(User.class)
                .eq(User::getId, userId)
                .eq(User::getPassword, password);
        User user = userMapper.selectOne(queryUserWrapper);
        if (Objects.isNull(user)) {
            throw new DaMaiFrameException(BaseCode.NAME_PASSWORD_ERROR);
        }
        
        // 2. 将用户信息存入Redis缓存（包含渠道码）
        redisCache.set(
            RedisKeyBuild.createRedisKey(RedisKeyManage.USER_LOGIN, code, user.getId()),
            user, 
            tokenExpireTime, 
            TimeUnit.MINUTES);
            
        // 3. 生成Token
        userLoginVo.setUserId(userId);
        userLoginVo.setToken(createToken(user.getId(), getChannelDataByCode(code).getTokenSecret()));
        
        return userLoginVo;
    }
    
    /**
     * 创建Token
     */
    public String createToken(Long userId, String tokenSecret) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("userId", userId);
        return TokenUtil.createToken(
            String.valueOf(uidGenerator.getUid()), 
            JSON.toJSONString(map),
            tokenExpireTime * 60 * 1000, 
            tokenSecret);
    }
    
    /**
     * 用户登出
     */
    public Boolean logout(UserLogoutDto userLogoutDto) {
        String userStr = TokenUtil.parseToken(
            userLogoutDto.getToken(),
            getChannelDataByCode(userLogoutDto.getCode()).getTokenSecret());
            
        if (StringUtil.isEmpty(userStr)) {
            throw new DaMaiFrameException(BaseCode.USER_EMPTY);
        }
        
        String userId = JSONObject.parseObject(userStr).getString("userId");
        // 删除Redis中的用户缓存
        redisCache.del(RedisKeyBuild.createRedisKey(
            RedisKeyManage.USER_LOGIN, userLogoutDto.getCode(), userId));
        return true;
    }
}
````
</augment_code_snippet>

## 6. 权限控制配置

### 6.1 网关权限配置

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/property/GatewayProperty.java" mode="EXCERPT">
````java
@Data
@Component
public class GatewayProperty {
    
    /**
     * 需要做频率限制的路径
     */
    @Value("${api.limit.paths:#{null}}")
    private String[] apiRestrictPaths;
    
    /**
     * 需要Token验证的路径
     */
    @Value("${skip.check.token.paths:/**/program/order/create/v1,/**/program/order/create/v2," +
           "/**/program/order/create/v3,/**/program/order/create/v4,/**/ticket/user/add," +
           "/**/ticket/user/delete,/**/ticket/user/list,/**/user/authentication," +
           "/**/user/update,/**/user/update/email,/**/user/update/mobile,/**/user/update/password," +
           "/**/order/cancel,/**/order/create,/**/order/pay,/**/order/select/list,/**/order/get}")
    private String[] checkTokenPaths;
    
    /**
     * 跳过参数验证的路径
     */
    @Value("${skip.check.parmeter.paths:/**/alipay/notify}")
    private String[] checkSkipParmeterPaths;
    
    /**
     * 是否允许普通访问（不验证签名）
     */
    @Value("${allow.normal.access:true}")
    private boolean allowNormalAccess;
    
    /**
     * 需要用户ID的路径
     */
    @Value("${userId.paths:/**/program/detail,/**/program/detail/v1,/**/program/detail/v2}")
    private String[] userIdPaths;
}
````
</augment_code_snippet>

### 6.2 路径权限检查方法

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/filter/RequestValidationFilter.java" mode="EXCERPT">
````java
/**
 * 检查是否跳过Token验证
 */
public boolean skipCheckToken(String url) {
    for (String skipCheckTokenPath : gatewayProperty.getCheckTokenPaths()) {
        PathMatcher matcher = new AntPathMatcher();
        if (matcher.match(skipCheckTokenPath, url)) {
            return false;  // 匹配到需要验证Token的路径
        }
    }
    return true;  // 跳过Token验证
}

/**
 * 检查是否跳过参数验证
 */
public boolean skipCheckParameter(String url) {
    for (String skipCheckTokenPath : gatewayProperty.getCheckSkipParmeterPaths()) {
        PathMatcher matcher = new AntPathMatcher();
        if (matcher.match(skipCheckTokenPath, url)) {
            return true;  // 跳过参数验证
        }
    }
    return false;
}

/**
 * 检查是否需要用户ID
 */
private boolean checkNeedUserId(String url) {
    for (String userIdPath : gatewayProperty.getUserIdPaths()) {
        PathMatcher matcher = new AntPathMatcher();
        if (matcher.match(userIdPath, url)) {
            return true;  // 需要用户ID
        }
    }
    return false;
}
````
</augment_code_snippet>

## 7. API限流机制

### 7.1 限流配置

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/pro/limit/RateLimiterProperty.java" mode="EXCERPT">
````java
@Data
public class RateLimiterProperty {
    
    @Value("${rate.switch:false}")
    private Boolean rateSwitch;  // 限流开关
    
    @Value("${rate.permits:200}")
    private Integer ratePermits;  // 限流许可数
}
````
</augment_code_snippet>

### 7.2 API限制服务

<augment_code_snippet path="damai-server/damai-gateway-service/src/main/java/com/damai/service/ApiRestrictService.java" mode="EXCERPT">
````java
@Component
public class ApiRestrictService {
    
    /**
     * 检查API是否需要限制
     */
    public boolean checkApiRestrict(String requestUri) {
        if (gatewayProperty.getApiRestrictPaths() != null) {
            for (String apiRestrictPath : gatewayProperty.getApiRestrictPaths()) {
                PathMatcher matcher = new AntPathMatcher();
                if (matcher.match(apiRestrictPath, requestUri)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * API限制处理
     */
    public void apiRestrict(String id, String url, ServerHttpRequest request) {
        if (checkApiRestrict(url)) {
            String ip = getIpAddress(request);
            StringBuilder stringBuilder = new StringBuilder(ip);
            if (StringUtil.isNotEmpty(id)) {
                stringBuilder.append("_").append(id);
            }
            
            // 使用Lua脚本进行原子性限流检查
            // 具体限流逻辑在Redis中执行
        }
    }
}
````
</augment_code_snippet>

## 8. 鉴权机制特点总结

### 8.1 多层次安全保障

1. **网络层**：通过网关统一入口控制
2. **应用层**：渠道验证和RSA签名验证
3. **用户层**：JWT Token身份认证
4. **接口层**：基于路径的权限控制
5. **流量层**：API限流和频率控制

### 8.2 多租户隔离

- 通过渠道码(code)实现多租户隔离
- 不同渠道使用不同的RSA密钥对
- 不同渠道使用不同的Token密钥
- Redis缓存key包含渠道码，实现数据隔离

### 8.3 高性能设计

- Redis缓存用户信息，减少数据库查询
- 渠道配置缓存，提高验证效率
- 异步处理API调用记录
- Lua脚本实现原子性限流操作

### 8.4 安全性保障

- RSA SHA256签名防止请求篡改
- JWT Token防止身份伪造
- Token过期机制防止长期有效性风险
- 多重验证确保请求合法性

### 8.5 灵活配置

- 可配置的路径权限控制
- 可开关的限流机制
- 可调节的Token过期时间
- 支持跳过验证的特殊路径

这套鉴权机制通过多层次的安全验证，确保了系统的安全性、可扩展性和高性能，为大麦购票系统提供了完善的安全保障。
