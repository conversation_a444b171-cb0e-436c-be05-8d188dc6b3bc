# 订单创建V4版本延迟队列实现详解

## 目录
- [1. 概述](#1-概述)
- [2. 延迟队列架构设计](#2-延迟队列架构设计)
- [3. 消息发送实现](#3-消息发送实现)
- [4. 消息消费实现](#4-消息消费实现)
- [5. 订单取消处理](#5-订单取消处理)
- [6. Redis缓存和数据库操作](#6-redis缓存和数据库操作)
- [7. 配置说明](#7-配置说明)
- [8. V4版本特点](#8-v4版本特点)

## 1. 概述

订单创建V4版本的延迟队列基于 **Redisson** 实现，主要用于处理以下场景：
- 订单自动取消（10分钟未支付）
- 数据最终一致性保障（V1-V3版本）
- 异步数据同步处理

### 1.1 核心组件

| 组件 | 作用 | 位置 |
|------|------|------|
| DelayQueueContext | 延迟队列上下文管理 | damai-service-delay-queue-framework |
| DelayOperateProgramDataSend | 数据同步消息发送器 | damai-order-service |
| DelayOrderCancelConsumer | 订单取消消息消费者 | damai-order-service |
| DelayOperateProgramDataConsumer | 数据同步消息消费者 | damai-program-service |

## 2. 延迟队列架构设计

### 2.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息发送者     │───▶│   延迟队列       │───▶│   消息消费者     │
│ (Producer)      │    │ (DelayQueue)    │    │ (Consumer)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ 分片选择器       │    │ Redis延迟队列    │    │ 线程池处理       │
│ (Sharding)      │    │ (Redisson)      │    │ (ThreadPool)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心上下文类

```java
public class DelayQueueContext {
    private final DelayQueueBasePart delayQueueBasePart;
    // key为topic主题，value为发送消息的处理器
    private final Map<String, DelayQueueProduceCombine> delayQueueProduceCombineMap = new ConcurrentHashMap<>();
    
    /**
     * 发送延时消息
     * @param topic 主题
     * @param content 消息内容
     * @param delayTime 延时时间
     * @param timeUnit 时间单位
     */
    public void sendMessage(String topic, String content, long delayTime, TimeUnit timeUnit) {
        DelayQueueProduceCombine delayQueueProduceCombine = delayQueueProduceCombineMap.computeIfAbsent(
                topic, k -> new DelayQueueProduceCombine(delayQueueBasePart, topic));
        delayQueueProduceCombine.offer(content, delayTime, timeUnit);
    }
}
```

### 2.3 分片设计

延迟队列采用分片设计提高并发性能：

```java
public class DelayQueueProduceCombine {
    private final IsolationRegionSelector isolationRegionSelector;
    private final List<DelayProduceQueue> delayProduceQueueList = new ArrayList<>();
    
    public DelayQueueProduceCombine(DelayQueueBasePart delayQueueBasePart, String topic) {
        Integer isolationRegionCount = delayQueueBasePart.getDelayQueueProperties().getIsolationRegionCount();
        isolationRegionSelector = new IsolationRegionSelector(isolationRegionCount);
        // 创建多个分片队列
        for(int i = 0; i < isolationRegionCount; i++) {
            delayProduceQueueList.add(new DelayProduceQueue(delayQueueBasePart.getRedissonClient(), topic + "-" + i));
        }
    }
    
    public void offer(String content, long delayTime, TimeUnit timeUnit) {
        int index = isolationRegionSelector.getIndex();
        // 选择分片发送消息
        delayProduceQueueList.get(index).offer(content, delayTime, timeUnit);
    }
}
```

## 3. 消息发送实现

### 3.1 延迟数据同步消息发送器

```java
@Component
public class DelayOperateProgramDataSend {
    
    @Autowired
    private DelayQueueContext delayQueueContext;
    
    /**
     * 发送延迟数据同步消息
     * 延时1秒后同步缓存数据到数据库
     */
    public void sendMessage(String message) {
        try {
            delayQueueContext.sendMessage(
                SpringUtil.getPrefixDistinctionName() + "-" + DELAY_OPERATE_PROGRAM_DATA_TOPIC,
                message, 
                DELAY_OPERATE_PROGRAM_DATA_TIME,      // 1秒
                DELAY_OPERATE_PROGRAM_DATA_TIME_UNIT  // 时间单位：秒
            );
        } catch (Exception e) {
            log.error("send message error message : {}", message, e);
        }
    }
}
```

### 3.2 发送时机

在订单状态更新时，针对V1-V3版本会发送延迟消息：

```java
// 在OrderService.updateProgramRelatedDataResolution方法中
if (!orderVersion.equals(ProgramOrderVersion.V4_VERSION.getValue())) {
    // V1-V3版本：先操作缓存，再发送延迟消息同步数据库
    orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
    if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
        programOperateDataDto.setSellStatus(SellStatus.SOLD.getCode());
        delayOperateProgramDataSend.sendMessage(JSON.toJSONString(programOperateDataDto));
    }
} else {
    // V4版本：直接同步调用，不使用延迟队列
    // ...
}
```

### 3.3 消息数据结构

发送的消息为JSON格式的ProgramOperateDataDto：

```java
public class ProgramOperateDataDto {
    private Long programId;              // 节目ID
    private List<Long> seatIdList;       // 座位ID列表
    private List<TicketCategoryCountDto> ticketCategoryCountDtoList; // 票档数量列表
    private Integer orderVersion;        // 订单版本
    private Integer sellStatus;          // 售卖状态
}
```

## 4. 消息消费实现

### 4.1 消费者接口定义

```java
public interface ConsumerTask {
    /**
     * 执行消费逻辑
     * @param content 消息内容
     */
    void execute(String content);
    
    /**
     * 返回消费的主题
     * @return 主题名称
     */
    String topic();
}
```

### 4.2 数据同步消费者

```java
@Component
public class DelayOperateProgramDataConsumer implements ConsumerTask {
    
    @Autowired
    private ProgramService programService;
    
    @Override
    public void execute(String content) {
        log.info("延迟操作节目数据消息进行消费 content : {}", content);
        
        if (StringUtil.isEmpty(content)) {
            log.error("延迟队列消息不存在");
            return;
        }
        
        try {
            // 解析数据同步消息
            ProgramOperateDataDto programOperateDataDto = JSON.parseObject(content, ProgramOperateDataDto.class);
            
            // 执行数据库同步操作
            programService.operateProgramData(programOperateDataDto);
            
            log.info("延迟数据同步完成，节目ID：{}", programOperateDataDto.getProgramId());
        } catch (Exception e) {
            log.error("延迟数据同步失败", e);
        }
    }
    
    @Override
    public String topic() {
        return SpringUtil.getPrefixDistinctionName() + "-" + DELAY_OPERATE_PROGRAM_DATA_TOPIC;
    }
}
```

### 4.3 订单取消消费者

```java
@Component
public class DelayOrderCancelConsumer implements ConsumerTask {
    
    @Autowired
    private OrderService orderService;
    
    @Override
    public void execute(String content) {
        log.info("延迟订单取消消息进行消费 content : {}", content);
        
        if (StringUtil.isEmpty(content)) {
            log.error("延迟队列消息不存在");
            return;
        }
        
        try {
            // 解析延时消息
            DelayOrderCancelDto delayOrderCancelDto = JSON.parseObject(content, DelayOrderCancelDto.class);
            
            // 执行订单取消逻辑
            OrderCancelDto orderCancelDto = new OrderCancelDto();
            orderCancelDto.setOrderNumber(delayOrderCancelDto.getOrderNumber());
            
            boolean cancel = orderService.cancel(orderCancelDto);
            if (cancel) {
                log.info("延迟订单取消成功 orderCancelDto : {}", content);
            } else {
                log.error("延迟订单取消失败 orderCancelDto : {}", content);
            }
        } catch (Exception e) {
            log.error("处理延迟订单取消消息失败", e);
        }
    }
    
    @Override
    public String topic() {
        return SpringUtil.getPrefixDistinctionName() + "-" + DELAY_ORDER_CANCEL_TOPIC;
    }
}
```

### 4.4 消费者线程池配置

```java
public class DelayConsumerQueue extends DelayBaseQueue {
    private final ThreadPoolExecutor executeTaskThreadPool;
    
    public DelayConsumerQueue(DelayQueuePart delayQueuePart, String relTopic) {
        // 配置消费线程池
        this.executeTaskThreadPool = new ThreadPoolExecutor(
            delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties().getCorePoolSize(),
            delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties().getMaximumPoolSize(),
            delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties().getKeepAliveTime(),
            delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties().getUnit(),
            new LinkedBlockingQueue<>(delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties().getWorkQueueSize()),
            r -> new Thread(Thread.currentThread().getThreadGroup(), r, 
                    "delay-queue-consume-thread-" + executeTaskThreadCount.getAndIncrement())
        );
    }
    
    public synchronized void listenStart() {
        if (!runFlag.get()) {
            runFlag.set(true);
            listenStartThreadPool.execute(() -> {
                while (!Thread.interrupted()) {
                    try {
                        String content = blockingQueue.take();
                        executeTaskThreadPool.execute(() -> {
                            try {
                                consumerTask.execute(content);
                            } catch (Exception e) {
                                log.error("consumer execute error", e);
                            }
                        });
                    } catch (InterruptedException e) {
                        destroy(executeTaskThreadPool);
                    } catch (Throwable e) {
                        log.error("blockingQueue take error", e);
                    }
                }
            });
        }
    }
}
```

## 5. 订单取消处理

### 5.1 订单取消流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant OS as 订单服务
    participant DQ as 延迟队列
    participant PS as 节目服务
    participant R as Redis
    participant DB as 数据库

    U->>OS: 创建订单
    OS->>DQ: 发送10分钟延迟取消消息
    OS->>R: 更新缓存数据
    OS->>DB: 保存订单数据

    Note over DQ: 10分钟后
    DQ->>OS: 消费取消消息
    OS->>OS: 检查订单状态
    alt 订单未支付
        OS->>DB: 更新订单状态为取消
        OS->>R: 恢复座位状态和库存
        OS->>PS: 同步数据到节目服务
    else 订单已支付
        OS->>OS: 忽略取消操作
    end
```

### 5.2 取消订单的核心逻辑

```java
@RepeatExecuteLimit(name = CANCEL_PROGRAM_ORDER, keys = {"#orderCancelDto.orderNumber"})
@ServiceLock(name = ORDER_CANCEL_LOCK, keys = {"#orderCancelDto.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public boolean cancel(OrderCancelDto orderCancelDto) {
    updateOrderRelatedData(orderCancelDto.getOrderNumber(), OrderStatus.CANCEL);
    return true;
}
```

### 5.3 订单状态检查

```java
public void checkOrderStatus(Order order) {
    if (Objects.isNull(order)) {
        throw new DaMaiFrameException(BaseCode.ORDER_NOT_EXIST);
    }
    if (Objects.equals(order.getOrderStatus(), OrderStatus.CANCEL.getCode())) {
        throw new DaMaiFrameException(BaseCode.ORDER_CANCEL);
    }
    if (Objects.equals(order.getOrderStatus(), OrderStatus.PAY.getCode())) {
        throw new DaMaiFrameException(BaseCode.ORDER_PAY);
    }
    if (Objects.equals(order.getOrderStatus(), OrderStatus.REFUND.getCode())) {
        throw new DaMaiFrameException(BaseCode.ORDER_REFUND);
    }
}
```

## 6. Redis缓存和数据库操作

### 6.1 数据库操作

当订单被取消时，系统会执行以下数据库操作：

```java
@Transactional(rollbackFor = Exception.class)
public void updateOrderRelatedData(Long orderNumber, OrderStatus orderStatus) {
    // 1. 查询订单信息
    Order order = orderMapper.selectOne(
        Wrappers.lambdaQuery(Order.class).eq(Order::getOrderNumber, orderNumber));
    checkOrderStatus(order);

    // 2. 查询购票人订单列表
    List<OrderTicketUser> orderTicketUserList = orderTicketUserMapper.selectList(
        Wrappers.lambdaQuery(OrderTicketUser.class).eq(OrderTicketUser::getOrderNumber, orderNumber));

    // 3. 更新订单状态
    Order updateOrder = new Order();
    updateOrder.setId(order.getId());
    updateOrder.setOrderStatus(orderStatus.getCode());
    updateOrder.setCancelOrderTime(DateUtils.now());

    // 4. 更新购票人订单状态
    OrderTicketUser updateOrderTicketUser = new OrderTicketUser();
    updateOrderTicketUser.setOrderStatus(orderStatus.getCode());
    updateOrderTicketUser.setCancelOrderTime(DateUtils.now());

    // 5. 批量更新
    orderMapper.update(updateOrder,
        Wrappers.lambdaUpdate(Order.class).eq(Order::getOrderNumber, orderNumber));
    orderTicketUserMapper.update(updateOrderTicketUser,
        Wrappers.lambdaUpdate(OrderTicketUser.class).eq(OrderTicketUser::getOrderNumber, orderNumber));

    // 6. 添加订单记录流水
    List<OrderTicketUserRecord> orderTicketUserRecordList = buildOrderRecords(orderTicketUserList, order);
    orderTicketUserRecordService.saveBatch(orderTicketUserRecordList);
}
```

### 6.2 Redis缓存操作

#### 6.2.1 用户订单数量调整

```java
// 取消订单时，用户下该节目的订单数量减1
if (Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {
    redisCache.incrBy(RedisKeyBuild.createRedisKey(
        RedisKeyManage.ACCOUNT_ORDER_COUNT, order.getUserId(), order.getProgramId()),
        -updateTicketUserOrderResult);
}
```

#### 6.2.2 座位状态恢复

```java
// 订单取消：座位状态改为未售卖
if (Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {
    seatHashKeyAdd = RedisKeyBuild.createRedisKey(
        RedisKeyManage.PROGRAM_SEAT_NO_SOLD_RESOLUTION_HASH, programId, k).getRelKey();
    for (SeatVo seatVo : v) {
        seatVo.setSellStatus(SellStatus.NO_SOLD.getCode());
    }
}

// 订单支付：座位状态改为已售卖
else if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
    seatHashKeyAdd = RedisKeyBuild.createRedisKey(
        RedisKeyManage.PROGRAM_SEAT_SOLD_RESOLUTION_HASH, programId, k).getRelKey();
    for (SeatVo seatVo : v) {
        seatVo.setSellStatus(SellStatus.SOLD.getCode());
    }
}
```

#### 6.2.3 库存恢复

```java
// 票档相关数据恢复
JSONObject jsonObject = new JSONObject();
jsonObject.put("programTicketRemainNumberHashKey", RedisKeyBuild.createRedisKey(
    RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION, programId, k).getRelKey());
jsonObject.put("ticketCategoryId", String.valueOf(k));
jsonObject.put("count", v.size()); // 恢复的余票数量
jsonArray.add(jsonObject);
```

#### 6.2.4 锁定座位释放

```java
// 释放锁定的座位
JSONObject unLockSeatIdjsonObject = new JSONObject();
unLockSeatIdjsonObject.put("programSeatLockHashKey", RedisKeyBuild.createRedisKey(
    RedisKeyManage.PROGRAM_SEAT_LOCK_RESOLUTION_HASH, programId, k).getRelKey());
unLockSeatIdjsonObject.put("unLockSeatIdList", v.stream()
    .map(SeatVo::getId).map(String::valueOf).collect(Collectors.toList()));
unLockSeatIdjsonArray.add(unLockSeatIdjsonObject);
```

### 6.3 Lua脚本执行

所有Redis操作通过Lua脚本保证原子性：

```java
List<String> keys = new ArrayList<>();
keys.add(String.valueOf(orderStatus.getCode()));  // 操作类型
keys.add(String.valueOf(programId));               // 节目ID
keys.add(RedisKeyBuild.getRedisKey(RedisKeyManage.PROGRAM_RECORD)); // 记录key
keys.add(recordTye + GLIDE_LINE + identifierId + GLIDE_LINE + userId); // 记录标识
keys.add(recordTye); // 记录类型

Object[] data = new String[4];
data[0] = JSON.toJSONString(unLockSeatIdjsonArray);    // 释放锁定座位
data[1] = JSON.toJSONString(addSeatDatajsonArray);     // 添加座位数据
data[2] = JSON.toJSONString(jsonArray);                // 恢复库存数据
data[3] = JSON.toJSONString(seatIdAndTicketUserIdDomainList); // 座位和购票人关系

// 执行Lua脚本
orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
```

## 7. 配置说明

### 7.1 延迟队列常量配置

```java
public class OrderConstant {
    // 订单取消延迟队列主题
    public static final String DELAY_ORDER_CANCEL_TOPIC = "d_delay_order_cancel_topic";

    // 数据同步延迟队列主题
    public static final String DELAY_OPERATE_PROGRAM_DATA_TOPIC = "d_delay_operate_program_data_topic";

    // 数据同步延迟时间：1秒
    public static final Long DELAY_OPERATE_PROGRAM_DATA_TIME = 1L;

    // 数据同步时间单位：秒
    public static final TimeUnit DELAY_OPERATE_PROGRAM_DATA_TIME_UNIT = TimeUnit.SECONDS;
}
```

### 7.2 延迟队列属性配置

```java
@Data
@ConfigurationProperties(prefix = "delay.queue")
public class DelayQueueProperties {

    /**
     * 从队列拉取数据的线程池中的核心线程数量，如果业务过慢可调大
     */
    private Integer corePoolSize = 4;

    /**
     * 从队列拉取数据的线程池中的最大线程数量，如果业务过慢可调大
     */
    private Integer maximumPoolSize = 4;

    /**
     * 从队列拉取数据的线程池中的最大线程回收时间
     */
    private long keepAliveTime = 30;

    /**
     * 时间单位
     */
    private TimeUnit unit = TimeUnit.SECONDS;

    /**
     * 从队列拉取数据的线程池中的队列数量，如果业务过慢可调大
     */
    private Integer workQueueSize = 256;

    /**
     * 延时队列的隔离分区数，延时有瓶颈时可调大，但会增大redis的cpu消耗
     * 同一个topic发送者和消费者的隔离分区数必须相同
     */
    private Integer isolationRegionCount = 5;
}
```

### 7.3 自动配置类

```java
@EnableConfigurationProperties(DelayQueueProperties.class)
public class DelayQueueAutoConfig {

    @Bean
    public DelayQueueInitHandler delayQueueInitHandler(DelayQueueBasePart delayQueueBasePart) {
        return new DelayQueueInitHandler(delayQueueBasePart);
    }

    @Bean
    public DelayQueueBasePart delayQueueBasePart(RedissonClient redissonClient,
                                                DelayQueueProperties delayQueueProperties) {
        return new DelayQueueBasePart(redissonClient, delayQueueProperties);
    }

    @Bean
    public DelayQueueContext delayQueueContext(DelayQueueBasePart delayQueueBasePart) {
        return new DelayQueueContext(delayQueueBasePart);
    }
}
```

## 8. V4版本特点

### 8.1 版本对比

| 特性 | V1-V3版本 | V4版本 |
|------|-----------|--------|
| 数据同步方式 | 延迟队列异步同步 | 直接同步调用 |
| 数据一致性 | 最终一致性 | 强一致性 |
| 性能特点 | 响应快，但可能存在短暂不一致 | 响应稍慢，但数据强一致 |
| 复杂度 | 较高（需要处理异步逻辑） | 较低（同步调用简单） |

### 8.2 V4版本实现

```java
// V4版本的处理逻辑
if (!orderVersion.equals(ProgramOrderVersion.V4_VERSION.getValue())) {
    // V1-V3版本：先操作缓存，再发送延迟消息
    orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
    if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())) {
        programOperateDataDto.setSellStatus(SellStatus.SOLD.getCode());
        delayOperateProgramDataSend.sendMessage(JSON.toJSONString(programOperateDataDto));
    }
} else {
    // V4版本：直接同步调用节目服务
    if (Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode()) ||
            Objects.equals(orderStatus.getCode(), OrderStatus.CANCEL.getCode())) {

        programOperateDataDto.setSellStatus(Objects.equals(orderStatus.getCode(), OrderStatus.PAY.getCode())
                ? SellStatus.SOLD.getCode() : SellStatus.NO_SOLD.getCode());

        // 同步调用节目服务API
        ApiResponse<Boolean> programApiResponse = programClient.operateProgramData(programOperateDataDto);
        if (!Objects.equals(programApiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
            throw new DaMaiFrameException(programApiResponse);
        }
    }
    // 仍然执行Redis缓存操作
    orderProgramCacheResolutionOperate.programCacheReverseOperate(keys, data);
}
```

### 8.3 V4版本优势

1. **数据强一致性**：通过同步调用保证缓存和数据库的强一致性
2. **简化架构**：减少了异步处理的复杂性
3. **错误处理**：同步调用可以立即感知错误并进行处理
4. **调试友好**：同步流程更容易调试和排查问题

### 8.4 适用场景

- **V1-V3版本**：适用于对响应时间要求极高，可以容忍短暂数据不一致的场景
- **V4版本**：适用于对数据一致性要求较高，可以接受稍长响应时间的场景

## 9. 最佳实践

### 9.1 监控和告警

1. **消息积压监控**：监控延迟队列中消息的积压情况
2. **消费失败告警**：当消息消费失败时及时告警
3. **性能监控**：监控消息处理的平均耗时

### 9.2 容错处理

1. **重试机制**：消费失败时的重试策略
2. **死信队列**：处理无法消费的消息
3. **降级策略**：当延迟队列不可用时的降级方案

### 9.3 性能优化

1. **分片数量调优**：根据业务量调整`isolationRegionCount`
2. **线程池调优**：根据消费速度调整线程池参数
3. **批量处理**：对于可以批量处理的消息进行批量优化

## 10. 总结

订单创建V4版本的延迟队列是一个基于Redisson实现的高性能异步处理框架，主要特点包括：

1. **分片设计**：通过多分片提高并发处理能力
2. **自动装配**：基于SpringBoot的自动配置机制
3. **灵活配置**：支持线程池和分片数量的灵活配置
4. **版本兼容**：同时支持V1-V3的异步模式和V4的同步模式

在实际使用中，需要根据业务特点选择合适的版本，并做好相应的监控和容错处理。
